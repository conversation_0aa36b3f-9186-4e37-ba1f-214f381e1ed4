-- CreateTable
CREATE TABLE `approval_emails` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `approved` B<PERSON><PERSON><PERSON>N NULL,
    `approvedOn` DATETIME(0) NULL,
    `emailTriggeredOn` DATETIME(0) NULL,
    `firstEmailTriggeredOn` DATETIME(0) NULL,
    `retriggerCounter` INTEGER NULL,
    `childUserId` INTEGER NULL,
    `guardianUserId` INTEGER NULL,

    INDEX `childUserId`(`childUserId`),
    INDEX `guardianUserId`(`guardianUserId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `calibration` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NULL,
    `calibration_date` DATE NULL,

    INDEX `user_id`(`user_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `calibration_data` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `value` DECIMAL(20, 15) NULL,
    `calibration_id` INTEGER NULL,

    INDEX `calibration_id`(`calibration_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `comments` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NOT NULL,
    `text` TEXT NOT NULL,
    `created_on` DATETIME(0) NOT NULL,

    INDEX `user_id`(`user_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `device_data` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `deviceId` VARCHAR(255) NOT NULL,
    `deviceOffset` INTEGER NULL,
    `height` INTEGER NULL,
    `pixelDensity` INTEGER NULL,
    `width` INTEGER NULL,
    `mmfor1Pixel` DECIMAL(5, 2) NULL,
    `model` VARCHAR(255) NULL,
    `os` VARCHAR(255) NULL,
    `pixelfor1mm` DECIMAL(5, 2) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `mapcog_legPers` (
    `dateAdded` VARCHAR(30) NULL,
    `username` VARCHAR(50) NOT NULL,
    `password` VARCHAR(50) NULL,
    `logindef` VARCHAR(50) NULL,

    PRIMARY KEY (`username`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `mapcog_patient` (
    `legPersId` VARCHAR(50) NULL,
    `sent` VARCHAR(1) NULL,
    `patientId` VARCHAR(50) NOT NULL,
    `firstName` TEXT NULL,
    `lastName` TEXT NULL,
    `age` INTEGER NULL,
    `gender` VARCHAR(1) NULL,
    `dateAdded` VARCHAR(30) NULL,

    INDEX `legPersId`(`legPersId`),
    PRIMARY KEY (`patientId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `mapcog_pause` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `resultId` INTEGER NOT NULL,
    `pauseTime` INTEGER NULL,
    `pauseLength` INTEGER NULL,

    INDEX `resultId`(`resultId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `mapcog_result` (
    `orderIndex` INTEGER NULL,
    `duration` INTEGER NULL,
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `testId` INTEGER NOT NULL,

    INDEX `testId`(`testId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `mapcog_test` (
    `sent` VARCHAR(1) NULL,
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `dateAdded` VARCHAR(30) NULL,
    `legPersId` VARCHAR(50) NULL,
    `patientId` VARCHAR(50) NULL,
    `medicine` TEXT NULL,
    `dose` TEXT NULL,
    `diagnosis` TEXT NULL,
    `comments` TEXT NULL,

    INDEX `legPersId`(`legPersId`),
    INDEX `patientId`(`patientId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `reading_chain` (
    `ID` INTEGER NOT NULL,

    INDEX `ID`(`ID`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `scheduled_notifications` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NULL,
    `title` VARCHAR(255) NOT NULL,
    `body` VARCHAR(255) NOT NULL,
    `scheduled_datetime` VARCHAR(255) NULL,
    `status` VARCHAR(255) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `scheduled_test` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NULL,
    `test_id` INTEGER NULL,
    `scheduled_datetime` TIMESTAMP(0) NULL,
    `completion_datetime` TIMESTAMP(0) NULL,
    `created_at` TIMESTAMP(0) NULL,
    `updated_at` TIMESTAMP(0) NULL,
    `is_mandatory` BOOLEAN NULL,
    `notice_period` INTEGER NULL DEFAULT 3,

    INDEX `scheduled_test_ibfk_1`(`user_id`),
    INDEX `scheduled_test_ibfk_2`(`test_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `sub_admin_assigned_users` (
    `subAdminId` INTEGER NULL,
    `userId` INTEGER NULL,

    INDEX `subAdminId`(`subAdminId`),
    INDEX `userId`(`userId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `sub_admin_data` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `Email` VARCHAR(255) NOT NULL,
    `FirstName` VARCHAR(255) NULL,
    `LastName` VARCHAR(255) NULL,
    `MobileNumber` VARCHAR(255) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `test` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NULL,
    `name_sv` VARCHAR(255) NULL,
    `created_at` TIMESTAMP(0) NOT NULL,
    `updated_at` TIMESTAMP(0) NOT NULL,
    `is_mandatory` BOOLEAN NOT NULL DEFAULT false,
    `notice_period` INTEGER NULL DEFAULT 3,
    `color` VARCHAR(255) NOT NULL,
    `active` BOOLEAN NULL DEFAULT true,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `training_session` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NULL,
    `start_datetime` DATETIME(0) NULL,
    `end_datetime` DATETIME(0) NULL,
    `type` INTEGER NULL,
    `source` INTEGER NULL,
    `offset` DOUBLE NULL,
    `oscillation_time` INTEGER NULL,
    `pendulum_length` DOUBLE NULL,
    `speed` DOUBLE NULL,
    `duration` DOUBLE NULL,
    `created_at` TIMESTAMP(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
    `updated_at` TIMESTAMP(0) NULL DEFAULT CURRENT_TIMESTAMP(0),

    INDEX `user_id`(`user_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `uuid` VARCHAR(191) NULL,
    `registered_on` TIMESTAMP(0) NULL,
    `starred` BOOLEAN NULL DEFAULT false,
    `type` VARCHAR(100) NULL,
    `deleted` BOOLEAN NULL DEFAULT false,
    `parent_info_id` INTEGER NULL,

    UNIQUE INDEX `user_uuid_key`(`uuid`),
    INDEX `user_parent_info_id_fkey`(`parent_info_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user_additional_info` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(200) NULL,
    `email` VARCHAR(200) NULL,
    `age` INTEGER NULL,
    `vision_problem` VARCHAR(200) NULL,
    `optional_text` TEXT NULL,
    `accept_newsletter` BOOLEAN NULL DEFAULT false,
    `user_id` INTEGER NOT NULL,
    `birthdate` DATETIME(3) NULL,
    `first_name` VARCHAR(200) NULL,
    `last_name` VARCHAR(200) NULL,
    `notification_hour` INTEGER NULL,
    `training_question_id` INTEGER NOT NULL DEFAULT 1,
    `change_flag` BOOLEAN NULL DEFAULT 0,
    `read_calibration_from_backend` BOOLEAN NULL DEFAULT 0,

    UNIQUE INDEX `user_additional_info_user_id_key`(`user_id`),
    INDEX `user_additional_info_user_fk`(`user_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `parent_info` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `first_name` VARCHAR(255) NULL,
    `last_name` VARCHAR(255) NULL,
    `email` VARCHAR(255) NULL,

    UNIQUE INDEX `parent_info_email_key`(`email`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user_guardian` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NOT NULL,
    `guardian_id` INTEGER NOT NULL,

    INDEX `user_guardian_guardian_id_fkey`(`guardian_id`),
    INDEX `user_guardian_user_id_fkey`(`user_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `vergence_answer` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `vergence_question_id` INTEGER NOT NULL,
    `deleted` BOOLEAN NULL DEFAULT false,
    `answer` VARCHAR(100) NULL,
    `value` INTEGER NULL,

    INDEX `vergence_answer_question_fk`(`vergence_question_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `vergence_question` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `title` VARCHAR(100) NULL,
    `language` VARCHAR(20) NULL,
    `deleted` BOOLEAN NULL DEFAULT false,
    `sequence` SMALLINT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `vergence_user_answer` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `session_id` INTEGER NULL,
    `answer_id` INTEGER NULL,
    `deleted` BOOLEAN NULL,

    INDEX `vergence_user_answer_vergence_answer_fk`(`answer_id`),
    INDEX `vergence_user_answer_vergence_user_session_fk`(`session_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `vergence_user_session` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NULL,
    `implemented_by_guardian` BOOLEAN NULL DEFAULT false,
    `created_at` DATETIME(0) NULL,
    `deleted` BOOLEAN NULL,

    INDEX `vergence_user_session_user__fk`(`user_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `pd_measurement` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `optician_number` FLOAT NOT NULL,
    `measured_number` FLOAT NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `training_questions_data` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NOT NULL,
    `question_id` INTEGER NOT NULL,
    `question` VARCHAR(100) NOT NULL,
    `answer_id` INTEGER NOT NULL,
    `answer` VARCHAR(100) NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `purchase` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `email` VARCHAR(191) NOT NULL,
    `code` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `duration` INTEGER NOT NULL,
    `first_name` VARCHAR(191) NOT NULL,
    `is_subscription` BOOLEAN NOT NULL,
    `last_name` VARCHAR(191) NOT NULL,
    `number_of_licenses` INTEGER NOT NULL,
    `number_of_vr_glasses` INTEGER NOT NULL,
    `order_number` VARCHAR(191) NOT NULL,
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `purchase_code_key`(`code`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `purchase_activation` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `purchase_id` INTEGER NOT NULL,
    `activation_date` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `user_id` INTEGER NULL,

    INDEX `activation_purchase__fk`(`purchase_id`),
    INDEX `purchase_activation_user_id_fkey`(`user_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user_survey` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NOT NULL,
    `question_index` INTEGER NOT NULL,
    `answer_index` INTEGER NOT NULL,

    INDEX `user_survey_user_id_fkey`(`user_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `school` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `city` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `binogi_permissions` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `school_id` INTEGER NOT NULL,
    `binogi_section_id` INTEGER NOT NULL,

    INDEX `binogi_permissions_school_id_fkey`(`school_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `school_class` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(191) NOT NULL,
    `school_id` INTEGER NOT NULL,

    INDEX `school_class_school_id_fkey`(`school_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `teacher` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `teacher_uuid` VARCHAR(191) NOT NULL,
    `first_name` VARCHAR(191) NOT NULL,
    `last_name` VARCHAR(191) NOT NULL,
    `role` VARCHAR(191) NULL,
    `report_frequency` INTEGER NULL DEFAULT 0,

    UNIQUE INDEX `teacher_teacher_uuid_key`(`teacher_uuid`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `student` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NULL,
    `first_name` VARCHAR(191) NULL,
    `last_name` VARCHAR(191) NULL,
    `birthdate` DATETIME(3) NULL,
    `email` VARCHAR(191) NULL,
    `purchase_id` INTEGER NULL,
    `class_id` INTEGER NULL,

    UNIQUE INDEX `student_user_id_key`(`user_id`),
    UNIQUE INDEX `student_email_key`(`email`),
    INDEX `student_class_id_fkey`(`class_id`),
    INDEX `student_purchase_id_fkey`(`purchase_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `student_teacher` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `student_id` INTEGER NOT NULL,
    `teacher_id` INTEGER NOT NULL,

    INDEX `studentId_idx`(`student_id`),
    INDEX `teacherId_idx`(`teacher_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `purchase_shipping_info` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `purchase_id` INTEGER NOT NULL,
    `shipping_date` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `tracking_number` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `purchase_shipping_info_purchase_id_key`(`purchase_id`),
    INDEX `purchase_shipping_info_purchase_id_fkey`(`purchase_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `purchase_order_status` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `order_id` VARCHAR(191) NOT NULL,
    `purchase_id` INTEGER NULL,
    `status` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `purchase_order_status_order_id_key`(`order_id`),
    UNIQUE INDEX `purchase_order_status_purchase_id_key`(`purchase_id`),
    INDEX `purchase_order_status_purchase_id_fkey`(`purchase_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `approval_emails` ADD CONSTRAINT `approval_emails_ibfk_1` FOREIGN KEY (`childUserId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- AddForeignKey
ALTER TABLE `approval_emails` ADD CONSTRAINT `approval_emails_ibfk_2` FOREIGN KEY (`guardianUserId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- AddForeignKey
ALTER TABLE `calibration` ADD CONSTRAINT `calibration_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- AddForeignKey
ALTER TABLE `calibration_data` ADD CONSTRAINT `calibration_data_ibfk_1` FOREIGN KEY (`calibration_id`) REFERENCES `calibration`(`id`) ON DELETE CASCADE ON UPDATE RESTRICT;

-- AddForeignKey
ALTER TABLE `comments` ADD CONSTRAINT `comments_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- AddForeignKey
ALTER TABLE `mapcog_patient` ADD CONSTRAINT `mapcog_patient_ibfk_1` FOREIGN KEY (`legPersId`) REFERENCES `mapcog_legPers`(`username`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- AddForeignKey
ALTER TABLE `mapcog_pause` ADD CONSTRAINT `mapcog_pause_resultId_fkey` FOREIGN KEY (`resultId`) REFERENCES `mapcog_result`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `mapcog_result` ADD CONSTRAINT `mapcog_result_testId_fkey` FOREIGN KEY (`testId`) REFERENCES `mapcog_test`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `mapcog_test` ADD CONSTRAINT `mapcog_test_ibfk_1` FOREIGN KEY (`legPersId`) REFERENCES `mapcog_legPers`(`username`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- AddForeignKey
ALTER TABLE `mapcog_test` ADD CONSTRAINT `mapcog_test_ibfk_2` FOREIGN KEY (`patientId`) REFERENCES `mapcog_patient`(`patientId`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- AddForeignKey
ALTER TABLE `scheduled_test` ADD CONSTRAINT `scheduled_test_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- AddForeignKey
ALTER TABLE `scheduled_test` ADD CONSTRAINT `scheduled_test_ibfk_2` FOREIGN KEY (`test_id`) REFERENCES `test`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- AddForeignKey
ALTER TABLE `sub_admin_assigned_users` ADD CONSTRAINT `sub_admin_assigned_users_ibfk_1` FOREIGN KEY (`subAdminId`) REFERENCES `sub_admin_data`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- AddForeignKey
ALTER TABLE `sub_admin_assigned_users` ADD CONSTRAINT `sub_admin_assigned_users_ibfk_2` FOREIGN KEY (`userId`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- AddForeignKey
ALTER TABLE `training_session` ADD CONSTRAINT `training_session_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE CASCADE ON UPDATE RESTRICT;

-- AddForeignKey
ALTER TABLE `user` ADD CONSTRAINT `user_parent_info_id_fkey` FOREIGN KEY (`parent_info_id`) REFERENCES `parent_info`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_additional_info` ADD CONSTRAINT `user_additional_info_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- AddForeignKey
ALTER TABLE `user_guardian` ADD CONSTRAINT `user_guardian_guardian_id_fkey` FOREIGN KEY (`guardian_id`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_guardian` ADD CONSTRAINT `user_guardian_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `vergence_answer` ADD CONSTRAINT `vergence_answer_question_fk` FOREIGN KEY (`vergence_question_id`) REFERENCES `vergence_question`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- AddForeignKey
ALTER TABLE `vergence_user_answer` ADD CONSTRAINT `vergence_user_answer_vergence_answer_fk` FOREIGN KEY (`answer_id`) REFERENCES `vergence_answer`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- AddForeignKey
ALTER TABLE `vergence_user_answer` ADD CONSTRAINT `vergence_user_answer_vergence_user_session_fk` FOREIGN KEY (`session_id`) REFERENCES `vergence_user_session`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- AddForeignKey
ALTER TABLE `vergence_user_session` ADD CONSTRAINT `vergence_user_session_user__fk` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- AddForeignKey
ALTER TABLE `purchase_activation` ADD CONSTRAINT `activation_purchase__fk` FOREIGN KEY (`purchase_id`) REFERENCES `purchase`(`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;

-- AddForeignKey
ALTER TABLE `purchase_activation` ADD CONSTRAINT `purchase_activation_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_survey` ADD CONSTRAINT `user_survey_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `binogi_permissions` ADD CONSTRAINT `binogi_permissions_school_id_fkey` FOREIGN KEY (`school_id`) REFERENCES `school`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `school_class` ADD CONSTRAINT `school_class_school_id_fkey` FOREIGN KEY (`school_id`) REFERENCES `school`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `student` ADD CONSTRAINT `student_class_id_fkey` FOREIGN KEY (`class_id`) REFERENCES `school_class`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `student` ADD CONSTRAINT `student_purchase_id_fkey` FOREIGN KEY (`purchase_id`) REFERENCES `purchase`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `student` ADD CONSTRAINT `student_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `student_teacher` ADD CONSTRAINT `student_teacher_student_id_fkey` FOREIGN KEY (`student_id`) REFERENCES `student`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `student_teacher` ADD CONSTRAINT `student_teacher_teacher_id_fkey` FOREIGN KEY (`teacher_id`) REFERENCES `teacher`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `purchase_shipping_info` ADD CONSTRAINT `purchase_shipping_info_purchase_id_fkey` FOREIGN KEY (`purchase_id`) REFERENCES `purchase`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `purchase_order_status` ADD CONSTRAINT `purchase_order_status_purchase_id_fkey` FOREIGN KEY (`purchase_id`) REFERENCES `purchase`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

