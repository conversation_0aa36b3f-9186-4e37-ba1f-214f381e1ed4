-- AlterTable
ALTER TABLE `purchase_additional_info` MODIFY `info` TEXT NULL;

-- AlterTable
ALTER TABLE `training_session_data` ADD COLUMN `offset` DECIMAL(10, 5) NULL,
    ADD COLUMN `oscillationTime` DECIMAL(10, 5) NULL,
    ADD COLUMN `pendlumLength` DECIMAL(10, 5) NULL,
    ADD COLUMN `speed` DECIMAL(10, 5) NULL;

-- AlterTable
ALTER TABLE `user_additional_info` ADD COLUMN `has_double_vision` BOOLEAN NULL DEFAULT false,
    ADD COLUMN `performed_test_today` BOOLEAN NULL DEFAULT false;

-- CreateTable
CREATE TABLE `admin_activities` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `admin_id` VARCHAR(191) NOT NULL,
    `action` VARCHAR(255) NOT NULL,
    `entity` VARCHAR(255) NULL,
    `entity_id` VARCHAR(191) NULL,
    `details` <PERSON><PERSON><PERSON> NULL,
    `ip_address` VARCHAR(45) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `admin_activities_admin_id_idx`(`admin_id`),
    INDEX `admin_activities_created_at_idx`(`created_at`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `admin_activities` ADD CONSTRAINT `admin_activities_admin_id_fkey` FOREIGN KEY (`admin_id`) REFERENCES `admin_user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
