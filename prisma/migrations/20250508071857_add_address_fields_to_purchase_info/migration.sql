/*
  Warnings:

  - You are about to alter the column `deviceOffset` on the `device_data` table. The data in that column could be lost. The data in that column will be cast from `Int` to `Double`.
  - You are about to alter the column `height` on the `device_data` table. The data in that column could be lost. The data in that column will be cast from `Int` to `Double`.
  - You are about to alter the column `pixelDensity` on the `device_data` table. The data in that column could be lost. The data in that column will be cast from `Int` to `Double`.
  - You are about to alter the column `width` on the `device_data` table. The data in that column could be lost. The data in that column will be cast from `Int` to `Double`.
  - You are about to alter the column `session_duration` on the `training_session_data` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,5)` to `Int`.
  - You are about to alter the column `offset` on the `training_session_data` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,5)` to `Double`.
  - You are about to alter the column `oscillationTime` on the `training_session_data` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,5)` to `Double`.
  - You are about to alter the column `pendlumLength` on the `training_session_data` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,5)` to `Double`.
  - You are about to alter the column `speed` on the `training_session_data` table. The data in that column could be lost. The data in that column will be cast from `Decimal(10,5)` to `Double`.

*/
-- DropForeignKey
ALTER TABLE `training_session_data` DROP FOREIGN KEY `training_session_data_user_id_fk`;

-- AlterTable
ALTER TABLE `calibration` ADD COLUMN `device_id` INTEGER NULL;

-- AlterTable
ALTER TABLE `device_data` ADD COLUMN `device_checksum` VARCHAR(64) NULL,
    ADD COLUMN `diagonalLength` DECIMAL(8, 2) NULL,
    ADD COLUMN `user_id` INTEGER NULL,
    MODIFY `deviceOffset` DOUBLE NULL,
    MODIFY `height` DOUBLE NULL,
    MODIFY `pixelDensity` DOUBLE NULL,
    MODIFY `width` DOUBLE NULL;

-- AlterTable
ALTER TABLE `purchase_additional_info` ADD COLUMN `address_line` VARCHAR(255) NULL,
    ADD COLUMN `city` VARCHAR(100) NULL,
    ADD COLUMN `country` VARCHAR(100) NULL,
    ADD COLUMN `order_amount` DECIMAL(10, 2) NULL,
    ADD COLUMN `phone` VARCHAR(50) NULL,
    ADD COLUMN `postal_code` VARCHAR(20) NULL,
    ADD COLUMN `purchase_source` ENUM('DEFAULT', 'ADMIN', 'WEBSHOP', 'IMPORTED') NULL DEFAULT 'IMPORTED',
    ADD COLUMN `purchase_type` ENUM('DEFAULT', 'SUBSCRIPTION', 'START_PACKAGE', 'CONTINUE_TRAINING') NULL DEFAULT 'DEFAULT',
    ADD COLUMN `state` VARCHAR(100) NULL;

-- AlterTable
ALTER TABLE `training_session_data` ADD COLUMN `device_id` INTEGER NULL,
    MODIFY `session_duration` INTEGER NOT NULL,
    MODIFY `type` VARCHAR(191) NULL,
    MODIFY `streamingSource` VARCHAR(191) NULL,
    MODIFY `offset` DOUBLE NULL,
    MODIFY `oscillationTime` DOUBLE NULL,
    MODIFY `pendlumLength` DOUBLE NULL,
    MODIFY `speed` DOUBLE NULL;

-- AlterTable
ALTER TABLE `user_additional_info` ADD COLUMN `starred` BOOLEAN NULL DEFAULT false;

-- CreateTable
CREATE TABLE `training_config` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NOT NULL,
    `left_eye_offset_x` DOUBLE NULL,
    `left_eye_offset_y` DOUBLE NULL,
    `left_eye_speed_x` DOUBLE NULL,
    `left_eye_speed_y` DOUBLE NULL,
    `left_eye_pendulum_x` DOUBLE NULL,
    `left_eye_pendulum_y` DOUBLE NULL,
    `right_eye_offset_x` DOUBLE NULL,
    `right_eye_offset_y` DOUBLE NULL,
    `right_eye_speed_x` DOUBLE NULL,
    `right_eye_speed_y` DOUBLE NULL,
    `right_eye_pendulum_x` DOUBLE NULL,
    `right_eye_pendulum_y` DOUBLE NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `training_config_user_id_fkey`(`user_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `reading_letters` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `value` VARCHAR(191) NOT NULL,
    `language` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `reading_words` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `value` VARCHAR(191) NOT NULL,
    `language` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `reading_sentences` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `value` VARCHAR(191) NOT NULL,
    `language` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `reading_sentences_chain` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `sentence_id` INTEGER NOT NULL,
    `language` VARCHAR(191) NOT NULL,
    `sentence` VARCHAR(191) NOT NULL,
    `correct_position_1` INTEGER NOT NULL,
    `correct_position_2` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `reading_sentences_chain_correct_times` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `chain_id` INTEGER NOT NULL,
    `time` DOUBLE NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `reading_sentences_chain_wrong_times` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `chain_id` INTEGER NOT NULL,
    `time` DOUBLE NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `reading_letters_chain` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `letter_id` INTEGER NOT NULL,
    `language` VARCHAR(191) NOT NULL,
    `letter` VARCHAR(191) NOT NULL,
    `correct_position_1` INTEGER NOT NULL,
    `correct_position_2` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `reading_letters_chain_correct_times` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `chain_id` INTEGER NOT NULL,
    `time` DOUBLE NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `reading_letters_chain_wrong_times` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `chain_id` INTEGER NOT NULL,
    `time` DOUBLE NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `reading_words_chain` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `word_id` INTEGER NOT NULL,
    `language` VARCHAR(191) NOT NULL,
    `word` VARCHAR(191) NOT NULL,
    `correct_position_1` INTEGER NOT NULL,
    `correct_position_2` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `reading_words_chain_correct_times` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `chain_id` INTEGER NOT NULL,
    `time` DOUBLE NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `reading_words_chain_wrong_times` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `chain_id` INTEGER NOT NULL,
    `time` DOUBLE NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `reading_test_data` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER NOT NULL,
    `test_date` DATETIME(3) NOT NULL,
    `language` VARCHAR(191) NOT NULL,
    `test_type` VARCHAR(191) NOT NULL,
    `score` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user_correct_answer_times` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `test_data_id` INTEGER NOT NULL,
    `chain_id` INTEGER NOT NULL,
    `time` VARCHAR(191) NOT NULL,
    `value` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user_wrong_answer_times` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `test_data_id` INTEGER NOT NULL,
    `chain_id` INTEGER NOT NULL,
    `time` VARCHAR(191) NOT NULL,
    `value` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `device_data` ADD CONSTRAINT `device_data_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `training_session_data` ADD CONSTRAINT `training_session_data_device_id_fkey` FOREIGN KEY (`device_id`) REFERENCES `device_data`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `training_session_data` ADD CONSTRAINT `training_session_data_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `training_config` ADD CONSTRAINT `training_config_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `user`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `reading_sentences_chain_correct_times` ADD CONSTRAINT `reading_sentences_chain_correct_times_chain_id_fkey` FOREIGN KEY (`chain_id`) REFERENCES `reading_sentences_chain`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `reading_sentences_chain_wrong_times` ADD CONSTRAINT `reading_sentences_chain_wrong_times_chain_id_fkey` FOREIGN KEY (`chain_id`) REFERENCES `reading_sentences_chain`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `reading_letters_chain_correct_times` ADD CONSTRAINT `reading_letters_chain_correct_times_chain_id_fkey` FOREIGN KEY (`chain_id`) REFERENCES `reading_letters_chain`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `reading_letters_chain_wrong_times` ADD CONSTRAINT `reading_letters_chain_wrong_times_chain_id_fkey` FOREIGN KEY (`chain_id`) REFERENCES `reading_letters_chain`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `reading_words_chain_correct_times` ADD CONSTRAINT `reading_words_chain_correct_times_chain_id_fkey` FOREIGN KEY (`chain_id`) REFERENCES `reading_words_chain`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `reading_words_chain_wrong_times` ADD CONSTRAINT `reading_words_chain_wrong_times_chain_id_fkey` FOREIGN KEY (`chain_id`) REFERENCES `reading_words_chain`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_correct_answer_times` ADD CONSTRAINT `user_correct_answer_times_test_data_id_fkey` FOREIGN KEY (`test_data_id`) REFERENCES `reading_test_data`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_wrong_answer_times` ADD CONSTRAINT `user_wrong_answer_times_test_data_id_fkey` FOREIGN KEY (`test_data_id`) REFERENCES `reading_test_data`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- RedefineIndex
CREATE INDEX `user_id` ON `training_session_data`(`user_id`);
DROP INDEX `user_id_idx` ON `training_session_data`;
