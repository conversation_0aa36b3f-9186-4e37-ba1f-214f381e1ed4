import { ApiProperty } from '@nestjs/swagger';
import {
  IsE<PERSON>,
  IsNotEmpty,
  IsString,
  Is<PERSON><PERSON>D,
  IsDate,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';

export class SignUpDto {
  @ApiProperty({ description: 'The unique identifier for the user' })
  @IsNotEmpty()
  readonly uuid: string;

  @ApiProperty({ description: 'The first name of the user' })
  @IsNotEmpty()
  @IsString()
  readonly firstName: string;

  @ApiProperty({ description: 'The last name of the user' })
  @IsString()
  readonly lastName: string;

  @ApiProperty({ description: 'The email address of the user' })
  @IsNotEmpty()
  @IsEmail()
  readonly email: string;

  @ApiProperty({ description: 'The birthdate of the user' })
  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  readonly birthdate: Date;

  @ApiProperty({ description: 'The account type' })
  readonly accountType: string;
}
