import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { DatabaseService } from 'src/database/database.service';
import { SignUpDto } from './dto/sign-up.dto';
import { FirebaseService } from 'src/firebase/firebase.service';
import * as moment from 'moment';
const {
  getFirestore,
  Timestamp,
  FieldValue,
  Filter,
} = require('firebase-admin/firestore');
@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  constructor(
    private database: DatabaseService,
    private firebaseService: FirebaseService,
  ) {}
  private calculateAge(birthDate: Date): number {
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  }
  async signUp(signUpDto: SignUpDto): Promise<{ user: any }> {
    const apiName = '[signUp API]';
    try {
      this.logger.log(`${apiName} Starting the sign-up process.`);
      // Destructure the signUpDto to extract needed information.
      const { firstName, lastName, email, birthdate, uuid, accountType } =
        signUpDto;
      // Get Firestore database reference.
      const db = this.firebaseService.getFirestore();
      // Reference to a specific document in the 'UserData' collection.
      const userRef = db.collection('UserData').doc(uuid);
      // Check if the document already exists to avoid duplicates.
      const doc = await userRef.get();
      if (doc.exists) {
        this.logger.error(
          `${apiName} A user with UUID ${uuid} already exists in Firestore.`,
        );
        throw new Error('A user with this UUID already exists in Firestore.');
      }
      // Calculate the user's age based on the provided birthdate.
      const birthDate = new Date(birthdate);
      const age = this.calculateAge(birthDate);
      // Prepare user data for Firestore.
      const userData = {
        uuid,
        FirstName: firstName,
        LastName: lastName,
        Email: email.toLowerCase(),
        Birthdate: Timestamp.fromDate(birthDate),
        Age: age,
        UserType: accountType,
        RegisteredOn: Timestamp.now(),
        Starred: false,
        Deleted: false,
        Verified: {},
      };
      // Check if a user with the given UUID already exists in the MariaDB.
      const existingUser = await this.database.user.findUnique({
        where: { uuid },
      });
      if (existingUser) {
        this.logger.error(
          `${apiName} A user with UUID ${uuid} already exists in MariaDB.`,
        );
        throw new Error('A user with this UUID already exists in MariaDB.');
      }
      // Save the new user's data to Firestore.
      await userRef.set(userData);
      this.logger.log(`${apiName} User data saved to Firestore.`);
      // Use a database transaction for atomic operations in MariaDB.
      const result = await this.database.$transaction(async (prisma) => {
        // Create a new user record.
        const user = await prisma.user.create({
          data: {
            uuid: uuid,
            registered_on: new Date(),
            starred: false,
            type: accountType,
            deleted: false,
          },
        });
        // Create additional info record for the user.
        const additionalInfo = await prisma.user_additional_info.create({
          data: {
            first_name: firstName,
            last_name: lastName,
            email: email.toLowerCase(),
            birthdate: birthDate,
            user_id: user.id,
            training_question_id: 1, // Set to 1 by default on creation. This is the id of question the user will be asked after training.
          },
        });
        this.logger.log(
          `${apiName} User record and additional info created in MariaDB.`,
        );

        // Return the newly created user.
        return { user, additionalInfo };
      });

      // Check if the given email matches any parent_info record.
      const parentInfo = await this.database.parent_info.findUnique({
        where: { email },
      });

      // If a parent is found, link the user to this parent as a guardian.
      if (parentInfo) {
        const childUser = await this.database.user.findFirst({
          where: { parent_info_id: parentInfo.id },
        });

        if (childUser) {
          // Create a guardian link between the child and the new user.
          await this.database.user_guardian.create({
            data: {
              user_id: childUser.id,
              guardian_id: result.user.id,
            },
          });
          // lookup firebase child account
          const firebaseChildAccountRef = db
            .collection('UserData')
            .doc(childUser.uuid);
          const firebaseChildAccount = await firebaseChildAccountRef.get();

          if (firebaseChildAccount.exists) {
            const childAccountDocId = childUser.uuid;
            const firebaseGuardianAccountQuery = db
              .collection('UserData')
              .where('Email', '==', email);

            const firebaseGuardianAccountSnapshot =
              await firebaseGuardianAccountQuery.get();

            // if firebase guardian account exists we link the two accounts
            if (!firebaseGuardianAccountSnapshot.empty) {
              const guardianDocRef =
                firebaseGuardianAccountSnapshot.docs[0].ref;
              let monitoringUsers =
                firebaseGuardianAccountSnapshot.docs[0].data()
                  .MonitoringUsers || [];
              monitoringUsers.push({
                UserApprovedMonitoring: true,
                UserId: db.doc(`UserData/${childAccountDocId}`),
              });

              await guardianDocRef.update({
                MonitoringUsers: monitoringUsers,
              });
            } else {
              // otherwise we attach the guardian email to child account for later reference
              await firebaseChildAccountRef.update({
                GuardianEmail: email,
              });
            }
          }
        }
      }
      this.logger.log(`${apiName} SignUp process completed successfully.`);
      return { user: result.user };
    } catch (error) {
      this.logger.error(
        `${apiName} Failed to sign up: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  // async login(loginDto: LoginDto): Promise<{ token: string }> {
  //   const { email, password } = loginDto;

  //   const user = await this.usersRepository.findOne({
  //     where: { email },
  //   });

  //   if (!user) {
  //     throw new UnauthorizedException('Invalid email or password');
  //   }

  //   const isPasswordMatched = await bcrypt.compare(password, user.password);

  //   if (!isPasswordMatched) {
  //     throw new UnauthorizedException('Invalid email or password');
  //   }

  //   const token = this.jwtService.sign({ id: user.id });

  //   return { token };
  // }
}
