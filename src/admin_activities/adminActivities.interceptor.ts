import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { AdminActivitiesService } from './adminActivities.service';

@Injectable()
export class AdminActivityInterceptor implements NestInterceptor {
  constructor(
    private readonly adminActivitiesService: AdminActivitiesService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const admin = request.user;

    return next.handle().pipe(
      tap(async () => {
        if (admin && this.shouldLogActivity(request)) {
          await this.adminActivitiesService.logActivity({
            admin_id: admin.id,
            action: this.getActionFromRequest(request),
            entity: this.getEntityFromRequest(request),
            entity_id: this.getEntityIdFromRequest(request),
            details: this.getDetailsFromRequest(request),
          });
        }
      }),
    );
  }

  private shouldLogActivity(request: any): boolean {
    const method = request.method;
    return ['POST', 'PUT', 'DELETE', 'PATCH'].includes(method);
  }

  private getActionFromRequest(request: any): string {
    return `${request.method} ${request.route.path}`;
  }

  private getEntityFromRequest(request: any): string {
    // Extract entity from the route path
    const path = request.route.path;
    const pathParts = path.split('/');
    return pathParts[1] || 'unknown';
  }

  private getEntityIdFromRequest(request: any): string {
    // Extract entity ID from params or body
    return request.params?.id || request.body?.id;
  }

  private getDetailsFromRequest(request: any): Record<string, any> {
    return {
      body: request.body,
      params: request.params,
      query: request.query,
    };
  }
}
