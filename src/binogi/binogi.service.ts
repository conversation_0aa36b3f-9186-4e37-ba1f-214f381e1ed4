import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { DatabaseService } from 'src/database/database.service';

@Injectable()
export class BinogiService {
  private availableFreeBinogiContentPermissions = [
    11304, 11379, 64253, 55686, 59605, 11427, 64216, 11462, 64782, 64804, 58629,
    11469,
  ];

  constructor(
    private httpService: HttpService,
    private database: DatabaseService,
  ) {}

  async getBinogiContentForUser(userUuid: string, lang: string) {
    try {
      // For API usage, if lang doesn't include a hyphen, use it directly.
      let languageCode = lang.includes('-') ? lang.split('-')[0] : lang;
      // Default to "sv" if languageCode isn't one of the allowed ones.
      const allowedLangs = ['en', 'sv', 'fi', 'es'];
      if (!allowedLangs.includes(languageCode)) {
        languageCode = 'sv';
      }

      // Fetch user by UUID.
      const user = await this.database.user.findUnique({
        where: { uuid: userUuid },
        select: { id: true },
      });
      if (!user) {
        throw new Error('User not found');
      }
      const userId = user.id;

      const student = await this.database.student.findUnique({
        where: { userId: userId },
        select: {
          class: {
            select: { schoolId: true, school: { select: { name: true } } },
          },
        },
      });

      // If no school is linked, return default free content.
      if (!student || !student.class || !student.class.schoolId) {
        return this.getContent(
          languageCode,
          this.availableFreeBinogiContentPermissions,
          languageCode,
        );
      }
      const schoolId = student.class.schoolId;

      // Fetch the binogi_permissions that match the school ID
      const allowedSections = await this.database.binogiPermissions.findMany({
        where: { school_id: schoolId },
        select: { binogi_section_id: true },
      });
      const allowedSectionIds = allowedSections.map(
        (perm) => perm.binogi_section_id,
      );

      return this.getContent(languageCode, allowedSectionIds, languageCode);
    } catch (error) {
      console.error('Error fetching filtered sections:', error.message);
      throw new Error('Unable to process the request: ' + error.message);
    }
  }

  private async getContent(
    lang: string,
    sectionIds: number[],
    inputLang: string,
  ) {
    let country_code = 'SE';
    if (lang.length === 2) {
      const mapping: Record<string, string> = {
        sv: 'SE',
        fi: 'FI',
      };
      country_code = mapping[lang.toLowerCase()] || 'SE';
    } else {
      const { parse } = await import('bcp-47');
      const parsed = parse(lang);
      if (parsed && parsed.region) {
        country_code = parsed.region.toUpperCase();
      } else {
        country_code = 'SE';
      }
    }
    let response;
    const apiUrl = `https://taxonomy.binogi.net/api/v2/taxonomy/published/country/${country_code}?requiredVideoLanguages=${lang}&acceptedVideoLocalizations=${country_code}`;

    try {
      response = await lastValueFrom(this.httpService.get(apiUrl));
    } catch (error) {
      console.error(
        `Error fetching data with language "${lang}", falling back to English.`,
      );
      const fallbackApiUrl = `https://taxonomy.binogi.net/api/v2/taxonomy/published/country/SE?requiredVideoLanguages=sv&acceptedVideoLocalizations=SE`;
      try {
        response = await lastValueFrom(this.httpService.get(fallbackApiUrl));
      } catch (fallbackError) {
        throw new Error(
          'Error fetching data from Binogi API (fallback to English failed): ' +
            fallbackError.message,
        );
      }
    }

    const taxonomy = response?.data;
    if (!taxonomy) {
      throw new Error('No data received from Binogi API');
    }

    // Process the taxonomy based on the given section IDs
    const categories = taxonomy
      .map((subject, subjectIndex) => {
        const subcategories = subject.chapters
          .map((chapter, chapterIndex) => {
            const sections = chapter.sections
              .filter((section) => sectionIds.includes(section.id))
              .map((section, sectionIndex) => {
                const videos = section.lessons.map((lesson, lessonIndex) => ({
                  index: lessonIndex + 1,
                  label: lesson.titles[lang] || lesson.titles['en'],
                  url: lesson.code
                    ? `https://stream.studi.se/stream/${lesson.code}/${lesson.code}_${country_code}_${lang}.mp4`
                    : '',
                }));
                return {
                  index: sectionIndex + 1,
                  label: section.titles[lang] || section.titles['en'],
                  videos: videos,
                };
              });

            if (sections.length > 0) {
              return {
                index: chapterIndex + 1,
                label: chapter.titles[lang] || chapter.titles['en'],
                subcategories: sections,
              };
            }
            return null;
          })
          .filter((subcategory) => subcategory !== null);

        let firstLessonCode = '';
        for (const chapter of subject.chapters) {
          for (const section of chapter.sections) {
            if (section.lessons.length > 0) {
              firstLessonCode = section.lessons[0].code;
              break;
            }
          }
          if (firstLessonCode) break;
        }

        return {
          index: subjectIndex + 1,
          label: subject.titles[lang] || subject.titles['en'],
          thumbnail: firstLessonCode
            ? this.getThumbnailForLesson(firstLessonCode, lang, country_code)
            : '',
          subcategories: subcategories,
        };
      })
      .filter((category) => category.subcategories.length > 0);

    return { categories };
  }

  getThumbnailForLesson(
    lessonCode: string,
    lang: string,
    country_code: string,
  ): string {
    return `https://stream.studi.se/stream/${lessonCode}/${lessonCode}_${country_code}_${lang}.jpg`;
  }
}
