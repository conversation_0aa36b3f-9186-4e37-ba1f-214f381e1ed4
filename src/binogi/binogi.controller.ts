import { Controller, Get, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { BinogiService } from './binogi.service';
import { GetFilteredSectionsDto } from './dto/get-filtered-sections.dto';

@ApiTags('Binogi')
@Controller('binogi')
export class BinogiController {
  constructor(private binogiService: BinogiService) {}

  @Get('/get-binogi-content')
  @ApiOperation({
    summary: 'Get filtered sections based on user and language',
    description:
      'Fetches sections available for the school of the given user, filtering the sections that the school has access to.',
  })
  @ApiResponse({
    status: 200,
    description: 'Filtered sections returned successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters',
  })
  async getBinogiContentForUser(@Query() query: GetFilteredSectionsDto) {
    return this.binogiService.getBinogiContentForUser(
      query.userUuid,
      query.lang || 'sv', // Default to Swedish if lang is not provided
    );
  }
}
