import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { WinstonModule } from 'nest-winston';
import * as winston from 'winston'; // Make sure to import winston
import { transports, format } from 'winston';
import { MailModule } from 'src/mail/mail.module';
import { BinogiController } from './binogi.controller';
import { BinogiService } from './binogi.service';
import { FirebaseModule } from 'src/firebase/firebase.module';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [FirebaseModule, MailModule, HttpModule],
  controllers: [BinogiController],
  providers: [BinogiService],
})
export class BinogiModule {}
