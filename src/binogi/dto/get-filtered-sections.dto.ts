import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';

export class GetFilteredSectionsDto {
  @ApiProperty({
    description: 'User ID of the student',
    example: '123',
  })
  @IsString()
  userUuid: string;

  @ApiProperty({
    description: 'Language code for video content (default: "sv" for Swedish)',
    example: 'sv',
    required: false,
  })
  @IsString()
  @IsOptional()
  lang?: string = 'sv'; // Default value
}
