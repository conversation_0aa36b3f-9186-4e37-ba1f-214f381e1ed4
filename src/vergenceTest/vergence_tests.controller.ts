import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Res,
} from '@nestjs/common';
import * as moment from 'moment';
import {
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Response } from 'express';
import { VergenceTestsService } from './vergence_tests.service';
import { AddVergenceAnswerDto } from './dto/add-vergence-answers.dto';
import { EditVergenceAnswerDto } from './dto/edit-vergence-answers.dto';
import { UpdateVergenceQuestionDto } from './dto/edit-vergence-questions.dto';
import { AddUserVergenceAnswersDto } from './dto/add-user-vergence-answer.dto';
import { CreateVergenceQuestionDto } from './dto/add-vergence-questions.dto';
import { GetVergenceResultsDto } from './dto/get-vergence-results.dto';
import { EditMultiQuestionsSequenceDto } from './dto/edit-multi-questions-sequence.dto';

@ApiTags('vergence-tests')
@Controller('vergence-tests')
export class VergenceTestsController {
  constructor(private vergenceTestsService: VergenceTestsService) {}

  @Get('/:vergenceQuestionId/vergence-answers')
  @ApiOperation({
    summary: 'Get vergence answers',
    description:
      'Fetches all answers for a specific vergence question based on its ID.',
  })
  @ApiParam({
    name: 'vergenceQuestionId',
    required: true,
    description: 'The unique identifier of the vergence question',
    type: Number,
  })
  async getVergenceAnswers(
    @Param('vergenceQuestionId', ParseIntPipe) vergenceQuestionId: number,
    @Res() res: Response,
  ) {
    try {
      const vergenceQuestion =
        await this.vergenceTestsService.getVergenceAnswers(vergenceQuestionId);
      if (!vergenceQuestion) {
        return res
          .status(HttpStatus.NOT_FOUND)
          .json({ error: 'Vergence question not found' });
      }
      res.json(vergenceQuestion.answers);
    } catch (error) {
      console.error(`Failed to fetch vergence questions: ${error}`);
      res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .send({ error: error.message });
    }
  }

  @Get('/vergence-questions')
  @ApiOperation({
    summary: 'Get all vergence questions',
    description: 'Fetches all vergence questions including their answers.',
  })
  async getVergenceQuestions(@Res() res: Response) {
    try {
      const vergenceQuestions =
        await this.vergenceTestsService.getVergenceQuestions();
      res.json(vergenceQuestions);
    } catch (error) {
      console.error(`Failed to fetch vergence questions: ${error}`);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).send({
        message: 'Failed to fetch vergence questions',
        error: error?.message,
      });
    }
  }

  @Post('/add-vergence-answer')
  @ApiOperation({
    summary: 'Add a vergence answer',
    description:
      'Creates a new vergence answer associated with a specific vergence question.',
  })
  async addVergenceAnswer(@Body() dto: AddVergenceAnswerDto) {
    try {
      const result = await this.vergenceTestsService.addVergenceAnswer(dto);
      return result;
    } catch (error) {
      throw new HttpException(
        'Failed to add vergence answer',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete('/delete-vergence-answer/:vergenceAnswerId')
  @ApiOperation({
    summary: 'Delete a vergence answer',
    description: 'Marks a vergence answer as deleted based on its ID.',
  })
  @ApiResponse({
    status: 200,
    description: 'Vergence answer deleted successfully.',
  })
  @ApiResponse({
    status: 400,
    description: 'Missing or invalid vergenceAnswerId parameter.',
  })
  @ApiResponse({ status: 500, description: 'Failed deleting vergence answer.' })
  async deleteVergenceAnswer(
    @Param('vergenceAnswerId', ParseIntPipe) vergenceAnswerId: number,
    @Res() res: Response,
  ) {
    try {
      const deletedVergenceAnswer =
        await this.vergenceTestsService.deleteVergenceAnswer(vergenceAnswerId);
      res.json(deletedVergenceAnswer);
    } catch (error) {
      console.error(`Failed deleting vergence answer: ${error}`);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).send({
        message: 'Failed deleting vergence answer',
        error: error?.message,
      });
    }
  }

  @Put('/edit-vergence-answer/:vergenceAnswerId')
  @ApiOperation({
    summary: 'Edit a vergence answer',
    description:
      'Updates a vergence answer based on its ID with new answer text and/or value.',
  })
  @ApiResponse({
    status: 200,
    description: 'Vergence answer updated successfully.',
  })
  @ApiResponse({
    status: 400,
    description: 'Missing or invalid vergenceAnswerId parameter.',
  })
  @ApiResponse({
    status: 500,
    description: 'Failed to update vergence answer.',
  })
  async editVergenceAnswer(
    @Param('vergenceAnswerId', ParseIntPipe) vergenceAnswerId: number,
    @Body() dto: EditVergenceAnswerDto,
    @Res() res: Response,
  ) {
    try {
      const updatedVergenceAnswer =
        await this.vergenceTestsService.editVergenceAnswer(
          vergenceAnswerId,
          dto,
        );
      res.json(updatedVergenceAnswer);
    } catch (error) {
      console.error(`Failed to update vergence answer: ${error}`);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).send({
        message: 'Failed to update vergence answer',
        error: error?.message,
      });
    }
  }

  @Delete('/delete-vergence-question/:vergenceQuestionId')
  @ApiOperation({ summary: 'Delete a vergence question' })
  @ApiParam({
    name: 'vergenceQuestionId',
    required: true,
    description: 'The ID of the vergence question to delete',
    type: Number,
  })
  async deleteVergenceQuestion(
    @Param('vergenceQuestionId', ParseIntPipe) vergenceQuestionId: number,
  ) {
    try {
      await this.vergenceTestsService.deleteVergenceQuestion(
        vergenceQuestionId,
      );
      return { message: 'Vergence question deleted successfully' };
    } catch (error) {
      throw new HttpException(
        'Failed to delete vergence question',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('/edit-vergence-question/:id')
  @ApiOperation({ summary: 'Update a vergence question' })
  @ApiResponse({
    status: 200,
    description: 'Vergence question updated successfully.',
  })
  @ApiResponse({ status: 400, description: 'Invalid input data.' })
  @ApiResponse({
    status: 500,
    description: 'Failed to update vergence question.',
  })
  updateVergenceQuestion(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateVergenceQuestionDto,
  ) {
    return this.vergenceTestsService.updateVergenceQuestion(id, dto);
  }

  @Post('add-user-vergence-answer')
  @ApiOperation({
    summary: 'Add User Vergence Answers',
    description:
      'This endpoint allows for the addition of vergence test answers for a user. If the user does not exist based on the provided UUID, a new user record will be created. Subsequently, a new vergence session is created to store the user’s answers.',
  })
  @ApiResponse({
    status: 201,
    description:
      'User vergence answers added successfully. Returns the user and vergence session details.',
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad request. Possibly due to missing or invalid request body parameters.',
  })
  @ApiResponse({
    status: 500,
    description:
      'Internal server error. Could be due to database connection issues or unhandled exceptions.',
  })
  addUserVergenceAnswers(@Body() dto: AddUserVergenceAnswersDto) {
    return this.vergenceTestsService.addUserVergenceAnswers(dto);
  }

  @Post('add-vergence-question')
  @ApiOperation({
    summary: 'Add a new vergence question',
    description:
      'Creates a new vergence question with the provided title, language, answers, and sequence.',
  })
  @ApiResponse({
    status: 201,
    description: 'The vergence question has been successfully created.',
  })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 500, description: 'Internal Server Error.' })
  addVergenceQuestion(
    @Body() addVergenceQuestionDto: CreateVergenceQuestionDto,
  ) {
    return this.vergenceTestsService.addVergenceQuestion(
      addVergenceQuestionDto,
    );
  }

  @Get('/results')
  @ApiOperation({ summary: 'Fetch vergence results for a user' })
  @ApiResponse({
    status: 200,
    description: 'Vergence results fetched successfully',
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({ status: 500, description: 'Failed to fetch vergence data' })
  async getVergenceResults(@Query('uuid') uuid: string) {
    try {
      const results = await this.vergenceTestsService.getVergenceResults(uuid);
      return results;
    } catch (error) {
      if (error.message === 'User not found') {
        throw new HttpException('User not found', HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to fetch vergence data',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':sessionId')
  @ApiOperation({ summary: 'Delete a vergence test' })
  @ApiResponse({
    status: 200,
    description: 'Vergence test deleted successfully',
  })
  @ApiResponse({ status: 400, description: 'Invalid session ID' })
  @ApiResponse({ status: 500, description: 'Failed to delete vergence test' })
  async deleteVergenceTest(
    @Param('sessionId', ParseIntPipe) sessionId: number,
  ) {
    try {
      return await this.vergenceTestsService.deleteVergenceTest(sessionId);
    } catch (error) {
      throw new HttpException(
        'Failed to delete vergence test',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put('/edit-questions-sequence')
  @ApiOperation({
    summary: 'Update sequences for multiple vergence questions',
    description:
      'Updates the sequence numbers for multiple vergence questions simultaneously.',
  })
  @ApiResponse({
    status: 200,
    description: 'Questions sequences updated successfully.',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data.',
  })
  @ApiResponse({
    status: 500,
    description: 'Failed to update questions sequences.',
  })
  async updateMultiQuestionsSequence(
    @Body() dto: EditMultiQuestionsSequenceDto,
    @Res() res: Response,
  ) {
    try {
      const updatedQuestions =
        await this.vergenceTestsService.updateMultiQuestionsSequence(dto);
      res.json(updatedQuestions);
    } catch (error) {
      console.error(`Failed to update questions sequences: ${error}`);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).send({
        message: 'Failed to update questions sequences',
        error: error?.message,
      });
    }
  }
}
