import {
  Injectable,
  Logger,
  HttpStatus,
  HttpException,
  NotFoundException,
} from '@nestjs/common';
import { DatabaseService } from 'src/database/database.service';
import { AddVergenceAnswerDto } from './dto/add-vergence-answers.dto';
import { EditVergenceAnswerDto } from './dto/edit-vergence-answers.dto';
import { UpdateVergenceQuestionDto } from './dto/edit-vergence-questions.dto';
import { CreateVergenceQuestionDto } from './dto/add-vergence-questions.dto';
import { AddUserVergenceAnswersDto } from './dto/add-user-vergence-answer.dto';
import { EditMultiQuestionsSequenceDto } from './dto/edit-multi-questions-sequence.dto';

@Injectable({})
export class VergenceTestsService {
  private readonly logger = new Logger(VergenceTestsService.name);

  constructor(private database: DatabaseService) {}

  async getVergenceAnswers(vergenceQuestionId: number) {
    try {
      this.logger.log(
        `VergenceTestsAPI - Retrieving answers for vergence question ID ${vergenceQuestionId}.`,
      );

      const answers = this.database.vergence_question.findUnique({
        where: { id: vergenceQuestionId },
        include: {
          answers: {
            select: {
              id: true,
              answer: true,
              value: true,
            },
            where: {
              deleted: false,
            },
          },
        },
      });
      if (!answers) {
        this.logger.warn(
          `VergenceTestsAPI - No answers found for vergence question ID ${vergenceQuestionId}.`,
        );
        throw new NotFoundException(
          `Answers for vergence question ID ${vergenceQuestionId} not found.`,
        );
      }

      this.logger.log(`VergenceTestsAPI - Successfully retrieved answers.`);
      return answers;
    } catch (error) {
      this.logger.error(
        `VergenceTestsAPI - Failed to retrieve answers - ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failure in retrieving vergence answers',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getVergenceQuestions() {
    try {
      this.logger.log(`VergenceTestsAPI - Fetching all vergence questions`);
      return await this.database.vergence_question.findMany({
        where: { deleted: false },
        select: {
          id: true,
          title: true,
          language: true,
          sequence: true,
          answers: {
            select: { id: true, answer: true, value: true },
            where: { deleted: false },
          },
        },
      });
    } catch (error) {
      this.logger.error(
        `VergenceTestsAPI - Error fetching vergence questions - ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Error fetching vergence questions',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async deleteVergenceQuestion(vergenceQuestionId: number): Promise<void> {
    try {
      this.logger.log(
        `VergenceTestsAPI - Attempting to delete vergence question with ID: ${vergenceQuestionId}`,
      );
      await this.database.vergence_question.update({
        where: { id: vergenceQuestionId },
        data: { deleted: true },
      });
      this.logger.log(
        `VergenceTestsAPI - Successfully deleted vergence question with ID: ${vergenceQuestionId}`,
      );
    } catch (error) {
      this.logger.error(
        `VergenceTestsAPI Failed to delete vergence question with ID: ${vergenceQuestionId}. Error: ${error}`,
      );
    }
  }
  async addVergenceAnswer(dto: AddVergenceAnswerDto) {
    try {
      this.logger.log(`VergenceTestsAPI - Adding vergence answer`);
      const answer = await this.database.vergence_answer.create({
        data: {
          answer: dto.answer,
          value: dto.value,
          vergence_question_id: dto.vergenceQuestionId,
        },
      });
      this.logger.log(
        `VergenceTestsAPI - Successfully added vergence answer ID: ${answer.id}`,
      );
      return answer;
    } catch (error) {
      this.logger.error(
        `VergenceTestsAPI - Failed to add vergence answer - ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to add vergence answer',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async deleteVergenceAnswer(vergenceAnswerId: number) {
    try {
      this.logger.log(
        `VergenceTestsAPI - Deleting vergence answer ID: ${vergenceAnswerId}`,
      );
      await this.database.vergence_answer.update({
        where: { id: vergenceAnswerId },
        data: { deleted: true },
      });
      this.logger.log(
        `VergenceTestsAPI - Successfully deleted vergence answer ID: ${vergenceAnswerId}`,
      );
    } catch (error) {
      this.logger.error(
        `VergenceTestsAPI - Failed to delete vergence answer ID: ${vergenceAnswerId} - ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to delete vergence answer',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async editVergenceAnswer(
    vergenceAnswerId: number,
    dto: EditVergenceAnswerDto,
  ) {
    try {
      this.logger.log(
        `VergenceTestsAPI - Editing vergence answer ID: ${vergenceAnswerId}`,
      );
      const answer = await this.database.vergence_answer.update({
        where: { id: vergenceAnswerId },
        data: dto,
      });
      this.logger.log(
        `VergenceTestsAPI - Successfully edited vergence answer ID: ${answer.id}`,
      );
      return answer;
    } catch (error) {
      this.logger.error(
        `VergenceTestsAPI - Failed to edit vergence answer ID: ${vergenceAnswerId} - ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to edit vergence answer',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateVergenceQuestion(id: number, dto: UpdateVergenceQuestionDto) {
    try {
      this.logger.log(
        `VergenceTestsAPI - Updating vergence question ID: ${id}`,
      );
      const updatedVergenceQuestion =
        await this.database.vergence_question.update({
          where: { id },
          data: dto,
        });
      this.logger.log(
        `VergenceTestsAPI - Successfully updated vergence question ID: ${updatedVergenceQuestion.id}`,
      );
      return updatedVergenceQuestion;
    } catch (error) {
      this.logger.error(
        `VergenceTestsAPI - Failed to update vergence question ID: ${id} - ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        `Failed to update vergence question: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async addVergenceQuestion(dto: CreateVergenceQuestionDto) {
    try {
      this.logger.log(`VergenceTestsAPI - Adding new vergence question.`);
      const question = await this.database.vergence_question.create({
        data: {
          title: dto.title,
          language: dto.language,
          sequence: dto.sequence,
          answers: {
            create: dto.answers,
          },
        },
      });
      this.logger.log(
        `VergenceTestsAPI - Successfully added vergence question ID: ${question.id}.`,
      );
      return question;
    } catch (error) {
      this.logger.error(
        `VergenceTestsAPI - Failed to add vergence question - ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to add vergence question',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async addUserVergenceAnswers(dto: AddUserVergenceAnswersDto): Promise<any> {
    try {
      this.logger.log('VergenceTestsAPI - Adding user vergence answers');
      const { userAdditionalInfo, testResult } = dto;
      let user = null;
      if (userAdditionalInfo.uuid) {
        // Upsert user based on UUID or create a new user
        user = await this.database.user.upsert({
          where: { uuid: userAdditionalInfo.uuid || undefined },
          create: {
            uuid: userAdditionalInfo.uuid,
            registered_on: new Date(),
            starred: false,
            type: 'standard',
            deleted: false,
            additional_info: {
              create: {
                name: userAdditionalInfo.name,
                email: userAdditionalInfo.email,
                training_question_id: 1, // Set to 1 by default on creation
              },
            },
          },
          update: {},
        });

        // Create vergence answers session
        await this.database.vergence_user_session.create({
          data: {
            user_id: user.id,
            created_at: new Date(),
            deleted: false,
            implemented_by_guardian: user.type == 'Guardian' ? true : false,
            vergence_user_answer: {
              createMany: {
                data: testResult.map((answer) => ({
                  answer_id: answer.answer_id,
                })),
              },
            },
          },
        });
      } else {
        user = await this.database.user.create({
          data: {
            uuid: null,
            registered_on: new Date(),
            starred: false,
            type: 'standard',
            deleted: false,
            additional_info: {
              create: {
                name: userAdditionalInfo.name,
                email: userAdditionalInfo.email,
                age: userAdditionalInfo.age
                  ? Number(userAdditionalInfo.age)
                  : null,
                vision_problem: userAdditionalInfo.vision_problem
                  ? userAdditionalInfo.vision_problem.join(',')
                  : null,
                optional_text: userAdditionalInfo.optional_text,
                accept_newsletter: userAdditionalInfo.accept_newsletter,
                training_question_id: 1,
              },
            },
            vergence_user_session: {
              create: {
                created_at: new Date(),
                implemented_by_guardian:
                  userAdditionalInfo.implemented_by_guardian,
                deleted: false,
                vergence_user_answer: {
                  createMany: {
                    data: testResult.map((answer) => ({
                      answer_id: answer.answer_id,
                    })),
                  },
                },
              },
            },
          },
          include: {
            vergence_user_session: {
              orderBy: {
                created_at: 'desc',
              },
              take: 1,
            },
          },
        });
        user.vergence_user_session[0];
      }

      this.logger.log(
        'VergenceTestsAPI - Successfully added user vergence answers',
      );
      return user;
    } catch (error) {
      this.logger.error(
        `VergenceTestsAPI - Error adding user vergence answers - ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Error adding user vergence answers',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getVergenceResults(uuid: string) {
    try {
      this.logger.log('VergenceTestsAPI - Fetching vergence results for user');
      const user = await this.database.user.findUnique({
        where: { uuid, deleted: false },
        include: { additional_info: true },
      });

      if (!user) {
        this.logger.error('VergenceTestsAPI - User not found');
        throw new NotFoundException('User not found');
      }

      // check if there are other users that have the same email in their additional_info
      const users = await this.database.user.findMany({
        where: {
          deleted: false,
          additional_info: {
            email: user.additional_info.email,
          },
        },
      });

      this.logger.log(
        `Found multiple users with the same email : ${
          user.additional_info.email
        } -> ${users.map((u) => u.id)}`,
      );

      // get sessions for these users based on their ids
      const userSessions = await this.database.vergence_user_session.findMany({
        where: { user_id: { in: users.map((u) => u.id) } },
        include: {
          vergence_user_answer: {
            where: { deleted: false },
            include: {
              answer: {
                include: {
                  vergence_question: {
                    include: {
                      answers: { where: { deleted: false } },
                    },
                  },
                },
              },
            },
          },
        },
      });

      const sessions = userSessions.map((session) => ({
        session: session.id,
        created_at: session.created_at,
        questions: session.vergence_user_answer.map((userAnswer) => ({
          question: userAnswer.answer.vergence_question.title,
          answer: userAnswer.answer.answer,
          value: userAnswer.answer.value,
          maxScore: Math.max(
            ...userAnswer.answer.vergence_question.answers.map((a) => a.value),
          ),
        })),
      }));
      this.logger.log(
        'VergenceTestsAPI - Successfully fetched vergence results for user',
      );
      return { sessions };
    } catch (error) {
      this.logger.error(
        `VergenceTestsAPI - Error fetching vergence results for user ${uuid} - ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Error fetching vergence results',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async deleteVergenceTest(sessionId: number): Promise<{ message: string }> {
    try {
      this.logger.log('VergenceTestsAPI - Deleting vergence test');
      if (isNaN(sessionId)) {
        throw new HttpException('Invalid session ID', HttpStatus.BAD_REQUEST);
      }

      await this.database.vergence_user_session.update({
        where: { id: sessionId },
        data: { deleted: true },
      });

      await this.database.vergence_user_answer.updateMany({
        where: { session_id: sessionId },
        data: { deleted: true },
      });

      const answersToDelete = await this.database.vergence_user_answer.findMany(
        {
          where: { session_id: sessionId, deleted: true },
          select: { answer_id: true },
        },
      );

      const answerIds = answersToDelete.map((a) => a.answer_id);

      if (answerIds.length > 0) {
        await this.database.vergence_answer.updateMany({
          where: { id: { in: answerIds } },
          data: { deleted: true },
        });
      }

      this.logger.log(
        'VergenceTestsAPI - Successfully marked vergence data as deleted',
      );
      return { message: 'Vergence data marked as deleted' };
    } catch (error) {
      this.logger.error(
        `VergenceTestsAPI - Error deleting vergence test ${sessionId} - ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Error deleting vergence test',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateMultiQuestionsSequence(dto: EditMultiQuestionsSequenceDto) {
    try {
      this.logger.log(
        `VergenceTestsAPI - Updating sequences for ${dto.questions.length} questions`,
      );
      const updates = dto.questions.map(({ id, sequence }) =>
        this.database.vergence_question.update({
          where: { id },
          data: { sequence },
        }),
      );

      const updatedQuestions = await this.database.$transaction(updates);

      this.logger.log(
        'VergenceTestsAPI - Successfully updated questions sequences',
      );

      return updatedQuestions;
    } catch (error) {
      this.logger.error(
        `VergenceTestsAPI - Failed to update questions sequences - ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to update questions sequences',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
