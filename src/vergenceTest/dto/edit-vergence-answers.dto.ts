import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, IsString, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class EditVergenceAnswerDto {
  @ApiProperty({
    description: 'Updated answer text for the vergence question.',
    required: false,
  })
  @IsOptional()
  @IsString()
  answer?: string;

  @ApiProperty({
    description: 'Updated value associated with the answer.',
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  value?: number;
}
