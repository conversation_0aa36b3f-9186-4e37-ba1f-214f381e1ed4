// src/vergenceTest/dto/edit-multi-questions-sequence.dto.ts

import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNumber, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

class QuestionSequence {
  @ApiProperty()
  @IsNumber()
  id: number;

  @ApiProperty()
  @IsNumber()
  sequence: number;
}

export class EditMultiQuestionsSequenceDto {
  @ApiProperty({ type: [QuestionSequence] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => QuestionSequence)
  questions: QuestionSequence[];
}