import { ApiProperty } from '@nestjs/swagger';
import { IsInt, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class GetVergenceAnswersDto {
  @ApiProperty({
    description: 'The unique identifier of the vergence question',
    example: 1,
  })
  @IsInt({ message: 'VergenceQuestionId must be an integer' })
  @Min(1, { message: 'VergenceQuestionId must be a positive integer' })
  @Type(() => Number) // Ensure transformation to number
  vergenceQuestionId: number;
}
