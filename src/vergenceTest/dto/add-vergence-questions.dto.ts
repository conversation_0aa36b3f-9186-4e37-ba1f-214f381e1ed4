import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsArray, ValidateNested, IsInt } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateVergenceAnswerDto } from './create-vergence-answer.dto';

export class CreateVergenceQuestionDto {
  @ApiProperty()
  @IsString()
  title: string;

  @ApiProperty()
  @IsString()
  language: string;

  @ApiProperty({ type: [CreateVergenceAnswerDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateVergenceAnswerDto)
  answers: CreateVergenceAnswerDto[];

  @ApiProperty()
  @IsInt()
  sequence: number;
}
