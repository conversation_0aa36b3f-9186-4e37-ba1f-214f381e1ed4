import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
  IsNumber,
  IsBoolean,
  IsDate,
  IsArray,
} from 'class-validator';
import { Type } from 'class-transformer';

export class UserAdditionalInfoDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  uuid?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  age?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  vision_problem?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  optional_text?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  accept_newsletter?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  birthdate?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  first_name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  last_name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  notification_hour?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  training_question_id?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  change_flag?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  read_calibration_from_backend?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  implemented_by_guardian?: boolean;
}

class TestResultDto {
  @ApiProperty()
  @IsNotEmpty()
  answer_id: number;
}

export class AddUserVergenceAnswersDto {
  @ApiProperty()
  @ValidateNested()
  @Type(() => UserAdditionalInfoDto)
  userAdditionalInfo: UserAdditionalInfoDto;

  @ApiProperty({ type: [TestResultDto] })
  @ValidateNested({ each: true })
  @Type(() => TestResultDto)
  testResult: TestResultDto[];
}
