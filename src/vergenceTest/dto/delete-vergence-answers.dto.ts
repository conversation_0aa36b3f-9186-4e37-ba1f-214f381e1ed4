import { ApiProperty } from '@nestjs/swagger';
import { IsInt, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class DeleteVergenceAnswerDto {
  @ApiProperty({
    description: 'The unique identifier of the vergence answer to be deleted.',
  })
  @IsInt({ message: 'VergenceAnswerId must be an integer' })
  @Min(1, { message: 'VergenceAnswerId must be a positive integer' })
  @Type(() => Number)
  vergenceAnswerId: number;
}
