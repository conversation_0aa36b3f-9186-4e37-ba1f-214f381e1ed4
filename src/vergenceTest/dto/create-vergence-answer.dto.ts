import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsString } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateVergenceAnswerDto {
  @ApiProperty({
    description: 'The answer text for the vergence question.',
  })
  @IsString()
  @IsNotEmpty()
  answer: string;

  @ApiProperty({
    description: 'The value associated with the answer.',
  })
  @IsInt()
  @Type(() => Number)
  value: number;
}
