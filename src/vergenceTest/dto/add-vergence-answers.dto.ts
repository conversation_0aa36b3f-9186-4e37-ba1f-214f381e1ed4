import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsString } from 'class-validator';
import { Type } from 'class-transformer';

export class AddVergenceAnswerDto {
  @ApiProperty({
    description: 'The answer text for the vergence question.',
  })
  @IsString()
  @IsNotEmpty()
  answer: string;

  @ApiProperty({
    description: 'The value associated with the answer.',
  })
  @IsInt()
  @Type(() => Number)
  value: number;

  @ApiProperty({
    description: 'The ID of the vergence question this answer belongs to.',
  })
  @IsInt()
  @Type(() => Number)
  vergenceQuestionId: number;
}
