import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { FirebaseService } from '../firebase/firebase.service';
import { MailService } from '../mail/mail.service';
import { EmailPasscodeDto } from './dto/email-passcode.dto';
import { IMVI_CIRCLE_BASE64 } from './base64Image';

@Injectable()
export class PasscodeService {
  private readonly logger = new Logger(PasscodeService.name);

  constructor(
    private firebaseService: FirebaseService,
    private mailService: MailService,
  ) {}

  async emailPasscode(
    data: EmailPasscodeDto,
  ): Promise<{ verificationCode: string }> {
    const apiName = '[emailPasscode API]';
    try {
      this.logger.log(
        `${apiName} Attempting to send passcode to email: ${data.email} for UID: ${data.uid}`,
      );
      const db = this.firebaseService.getFirestore();
      const userRef = db.collection('UserData').doc(data.uid);

      const doc = await userRef.get();

      if (!doc.exists) {
        this.logger.error(`${apiName} No user found with uid: ${data.uid}`);
        throw new HttpException(
          `No user found with uid: ${data.uid}`,
          HttpStatus.NOT_FOUND,
        );
      }

      const verificationCode = this.generateCode(4);
      const imageAsBase64 = IMVI_CIRCLE_BASE64.split(',')[1];
      const emailTemplate = {
        subject:
          data.language === 'sv'
            ? 'IMVI Verifieringskod'
            : 'IMVI Verification Code',
        text:
          data.language === 'sv'
            ? `Hej ${data.name},\n\nDin verifieringskod är: ${verificationCode}\n\nLycka till med din träning!\n\nMed vänliga hälsningar,\nIMVI Customer Success Team\<EMAIL>`
            : `Dear ${data.name},\n\nYour verification code is: ${verificationCode}\n\nGood luck with your training!\n\nKind regards,\nIMVI Customer Success Team\<EMAIL>`,
        html:
          data.language === 'sv'
            ? `<h3>Hej ${data.name},</h3><p>Din verifieringskod är: <strong>${verificationCode}</strong></p><p>Lycka till med din träning!</p><p>Med vänliga hälsningar,<br/>IMVI Customer Success Team<br/><a href='mailto:<EMAIL>'><EMAIL></a></p><img src='cid:IMVIlogo' alt='IMVI Logo' width='50' height='50' style='display: block' />`
            : `<h3>Dear ${data.name},</h3><p>Your verification code is: <strong>${verificationCode}</strong></p><p>Good luck with your training!</p><p>Kind regards,<br/>IMVI Customer Success Team<br/><a href='mailto:<EMAIL>'><EMAIL></a></p><img src='cid:IMVIlogo' alt='IMVI Logo' width='50' height='50' style='display: block' />`,
      };
      const inlinedAttachments = [
        {
          ContentType: 'image/png',
          Filename: 'IMVI_circle.png',
          ContentID: 'IMVIlogo',
          Base64Content: imageAsBase64,
        },
      ];
      emailTemplate.html = emailTemplate.html.replace(
        '<img src="cid:IMVIlogo" alt="IMVI Logo"  width="50" height="50" />',
        '<img src="cid:IMVIlogo" alt="IMVI Logo" width="50" height="50" />',
      );
      // Update and send email
      await userRef.update({
        Verified: {
          Code: verificationCode,
          VerificationDone: false,
        },
      });

      await this.mailService.sendMail(
        data.email,
        data.name,
        emailTemplate.subject,
        emailTemplate.text,
        emailTemplate.html,
        inlinedAttachments,
      );

      console.log('Email sent successfully');
      return { verificationCode };
    } catch (error) {
      this.logger.error(
        `${apiName} Failed to send verification email or update database for UID: ${data.uid}: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to process request',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private generateCode(length: number): string {
    let result = '';
    const characters = '0123456789';
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  }
}
