import { <PERSON>du<PERSON> } from '@nestjs/common';
import { WinstonModule } from 'nest-winston';
import * as winston from 'winston'; // Make sure to import winston
import { transports, format } from 'winston';
import { PasscodeController } from './email_passcode.controller';
import { PasscodeService } from './email_passcode.service';
import { FirebaseModule } from 'src/firebase/firebase.module';
import { MailModule } from 'src/mail/mail.module';

@Module({
  imports: [FirebaseModule, MailModule],
  controllers: [PasscodeController],
  providers: [PasscodeService],
})
export class PasscodeModule {}
