import {
  Body,
  Controller,
  HttpException,
  HttpStatus,
  Post,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { FirebaseService } from '../firebase/firebase.service';
import { MailService } from '../mail/mail.service';
import { PasscodeService } from './email_passcode.service';
import { EmailPasscodeDto } from './dto/email-passcode.dto';

@ApiTags('passcode')
@Controller('passcode')
export class PasscodeController {
  constructor(private passcodeService: PasscodeService) {}

  @Post('email-passcode')
  @ApiOperation({
    summary: 'Send verification email with passcode',
    description:
      "This endpoint triggers the sending of a verification email to the user. It expects a unique user identifier (uid), the user's email address, and the user's name. Upon successful execution, it generates a verification passcode, stores it for future verification, and sends an email containing this passcode to the specified email address. This passcode is used to verify the user's email address in subsequent steps of the verification process.",
  })
  @ApiResponse({
    status: 200,
    description: 'The verification passcode was sent successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'The provided UID was not found in the database.',
  })
  @ApiResponse({
    status: 500,
    description: 'Failed to process request',
  })
  async emailPasscode(@Body() data: EmailPasscodeDto) {
    try {
      const result = await this.passcodeService.emailPasscode(data);
      return result;
    } catch (error) {
      throw new HttpException(
        'Failed to process request',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
