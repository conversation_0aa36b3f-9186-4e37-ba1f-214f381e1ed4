import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class EmailPasscodeDto {
  @ApiProperty({
    description: 'The unique identifier for the user',
  })
  @IsString()
  @IsNotEmpty()
  uid: string;

  @ApiProperty({
    description: 'The email address to send the verification passcode',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'The name of the user',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({
    description: 'The language of the user (optional)',
  })
  @IsString()
  @IsOptional()
  language?: string;
}
