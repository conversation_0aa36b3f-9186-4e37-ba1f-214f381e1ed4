import {
  Body,
  Injectable,
  Inject,
  Res,
  Logger,
  HttpStatus,
  HttpException,
  NotFoundException,
} from '@nestjs/common';
import { DatabaseService } from 'src/database/database.service';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import { CreateScheduledTestDto } from './dto/create-scheduled-test.dto';
import { GetScheduledTestsQueryDto } from './dto/get-scheduled-test.dto';
import { PubSub } from '@google-cloud/pubsub';
import { EditScheduledTestDto } from './dto/edit-scheduled-test.dto';
import * as moment from 'moment';

@Injectable({})
export class ScheduledTestsService {
  private readonly logger = new Logger(ScheduledTestsService.name);
  private readonly pubsub = new PubSub();

  constructor(private database: DatabaseService) {}

  async updateScheduledTest(
    userUUID: string,
    scheduledTestID: number,
    dto: EditScheduledTestDto,
  ) {
    // Check if the user exists
    const user = await this.database.user.findUnique({
      where: { uuid: userUUID },
    });
    if (!user) {
      this.logger.error(`ScheduledTestsAPI - User not found: UUID ${userUUID}`);
      throw new NotFoundException('User not found');
    }

    // Log preparation for updating scheduled test
    this.logger.log(
      `ScheduledTestsAPI - Updating scheduled test ${scheduledTestID} for user ${userUUID}`,
    );

    // Prepare update data based on provided DTO fields
    const updateData: any = {};
    if (dto.scheduledDateTime)
      updateData.scheduled_datetime = new Date(dto.scheduledDateTime);
    if (dto.completionDateTime)
      updateData.completion_datetime = new Date(dto.completionDateTime);
    if (dto.testID !== undefined) updateData.test_id = dto.testID;
    if (dto.isMandatory !== undefined)
      updateData.is_mandatory = dto.isMandatory;
    if (dto.noticePeriod !== undefined)
      updateData.notice_period = dto.noticePeriod;

    // Attempt to update the scheduled test
    let updatedScheduledTest;
    try {
      updatedScheduledTest = await this.database.scheduled_test.update({
        where: { id: scheduledTestID },
        data: updateData,
      });
      this.logger.log(
        `ScheduledTestsAPI - Scheduled test ${scheduledTestID} updated successfully`,
      );
    } catch (error) {
      this.logger.error(
        `ScheduledTestsAPI - Failed to update the scheduled test: ${error.message}`,
      );
      throw new HttpException(
        'ScheduledTestsAPI - Failed to update the scheduled test',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    const trainingSessions = await this.database.training_session_data.findMany(
      {
        where: { user_id: user.id },
      },
    );
    // Check and publish to Pub/Sub if completionDateTime is set and the user has trained
    // This is to ensure the performed_test_today flag is not updated when the user performs initial tests
    // We should also exclude the performed test flag update if the test is pupilary distance (dto.testID != 2)
    if (dto.completionDateTime && trainingSessions.length > 0 && dto.testID != 2) {
      const topicName = 'projects/imvi-read/topics/update-performed-tests-flag';
      const messageData = JSON.stringify({ userUUID, scheduledTestID });
      const dataBuffer = Buffer.from(messageData);

      try {
        await this.pubsub.topic(topicName).publishMessage({ data: dataBuffer });
        this.logger.log(
          'ScheduledTestsAPI - Sent update performed test notification to Pub/Sub',
        );
      } catch (pubsubError) {
        this.logger.error(
          `ScheduledTestsAPI - Failed to send update performed test notification with error: ${pubsubError.message}`,
        );
      }

      try {
        const user = await this.database.user.findUnique({
          where: { uuid: userUUID },
          select: { id: true },
        });

        if (!user) {
          this.logger.error(
            `ScheduledTestsAPI - User with UUID ${userUUID} not found.`,
          );
          throw new Error(`User with UUID ${userUUID} not found.`);
        }

        await this.database.user_additional_info.update({
          where: { user_id: user.id },
          data: { performed_test_today: true },
        });

        this.logger.log(
          `ScheduledTestsAPI - Updated performed_test_today flag for user ID: ${user.id}`,
        );
      } catch (sqlError) {
        this.logger.error(
          `ScheduledTestsAPI - Failed to update performed_test_today flag with error: ${sqlError.message}`,
        );
      }
    }

    return updatedScheduledTest;
  }

  async addScheduledTest(userUUID: string, dto: CreateScheduledTestDto) {
    const methodName = 'addScheduledTest'; // For logging purposes
    try {
      this.logger.log(
        `ScheduledTestsAPI - ${methodName}: Attempting to add a scheduled test for User UUID: ${userUUID}`,
      );

      // Ensure the user exists or create a new one if not. No updates are performed if the user already exists.
      const user = await this.database.user.upsert({
        where: { uuid: userUUID },
        create: {
          uuid: userUUID,
          registered_on: new Date(),
          starred: false,
          type: 'standard',
          deleted: false,
        },
        update: {},
      });

      // Log user creation or retrieval success
      this.logger.log(
        `ScheduledTestsAPI - ${methodName}: User upsert successful for UUID: ${userUUID}`,
      );

      // Check if the specified test exists.
      const test = await this.database.test.findUnique({
        where: { id: dto.testID },
      });

      if (!test) {
        this.logger.error(
          `ScheduledTestsAPI - ${methodName}: Test not found with ID: ${dto.testID}`,
        );
        throw new HttpException('Test not found', HttpStatus.NOT_FOUND);
      }

      // Log the existence of the test

      // Create the scheduled test with details provided in the DTO.
      const result = await this.database.scheduled_test.create({
        data: {
          user_id: user.id,
          test_id: dto.testID,
          scheduled_datetime: new Date(dto.scheduledDateTime),
          created_at: new Date(),
          updated_at: new Date(),
          is_mandatory: dto.isMandatory || false,
          notice_period: dto.noticePeriod || 0,
        },
      });

      this.logger.log(
        `ScheduledTestsAPI - ${methodName}: Test found with ID: ${dto.testID}. Proceeding to create scheduled test.`,
      );

      // Add the Scheduled test to the scheduled notifications table in database for future push notifications.
      let testDate = new Date(dto.scheduledDateTime);
      testDate.setDate(testDate.getDate() - dto.noticePeriod);
      this.logger.log('Test Date after notice period reduce: ', testDate);
      testDate.setDate(testDate.getDate() + 1);
      this.logger.log('Test Date after one day added: ', testDate);

      const userAddInfo = await this.database.user_additional_info.findUnique({
        where: { user_id: user.id },
      });
      const name = userAddInfo.first_name;

      const title = `A New Test Has Been Scheduled!`;
      const body = `Hi ${name}! A new Test has been scheduled for you.`;

      const notifResult = await this.database.scheduled_notifications.create({
        data: {
          user_id: user.id,
          title: title,
          body: body,
          scheduled_datetime: new Date(testDate).toLocaleDateString(),
          status: 'Processing',
        },
      });

      // Log the successful creation of the scheduled test
      this.logger.log(
        `ScheduledTestsAPI - ${methodName}: Scheduled test successfully created with ID: ${result.id}`,
      );

      this.logger.log(
        `ScheduledTestsAPI - ${methodName}: Scheduled test notification successfully added with ID: ${notifResult.id}`,
      );

      return result;
    } catch (error) {
      // Log the error before re-throwing
      this.logger.error(
        `ScheduledTestsAPI - ${methodName}: Failed to add scheduled test for User UUID: ${userUUID} with error: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        `Failed to add scheduled test: ${error.message}`,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async deleteScheduledTest(userUUID: string, scheduledTestID: number) {
    const methodName = 'deleteScheduledTests';
    try {
      // Ensure the user exists before attempting to delete a test
      const user = await this.database.user.findUnique({
        where: { uuid: userUUID },
      });
      if (!user) {
        this.logger.error(
          `ScheduledTestsAPI - ${methodName}: User not found - UUID: ${userUUID}`,
        );
        throw new NotFoundException('User not found');
      }
      this.logger.log(
        `ScheduledTestsAPI - ${methodName}: deleting scheduled test ${scheduledTestID} for user UUID: ${userUUID}`,
      );
      // Delete the scheduled test and return the result
      return await this.database.scheduled_test.delete({
        where: { id: scheduledTestID },
      });
    } catch (error) {
      this.logger.error(
        `ScheduledTestsAPI - ${methodName}: Error deleting scheduled test ${scheduledTestID} for user UUID: ${userUUID} - ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to delete scheduled test',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findScheduledTestsForUser(
    userUUID: string,
    query: GetScheduledTestsQueryDto,
  ) {
    const methodName = 'findScheduledTestsForUser';
    try {
      // Check if the user exists
      const user = await this.database.user.findUnique({
        where: { uuid: userUUID },
      });

      if (!user) {
        this.logger.error(
          `ScheduledTestsAPI - ${methodName}: User not found - UUID: ${userUUID}`,
        );
        throw new NotFoundException('User not found');
      }

      // Construct the where clause for filtering tests by date
      const whereClause = {
        AND: [
          { user_id: user.id },
          {
            scheduled_datetime: {
              gte: query.startDateTime
                ? new Date(query.startDateTime)
                : undefined,
              lte: query.endDateTime ? new Date(query.endDateTime) : undefined,
            },
          },
        ],
      };

      this.logger.log(
        `ScheduledTestsAPI - ${methodName}: fetching scheduled tests for user UUID: ${userUUID}`,
      );

      // Fetch and return the filtered scheduled tests
      return await this.database.scheduled_test.findMany({
        where: whereClause,
        select: {
          id: true,
          scheduled_datetime: true,
          completion_datetime: true,
          is_mandatory: true,
          notice_period: true,
          created_at: true,
          updated_at: true,
          test: true,
          user: {
            select: {
              uuid: true,
              registered_on: true,
              starred: true,
              type: true,
              deleted: true,
            },
          },
        },
      });
    } catch (error) {
      this.logger.error(
        `ScheduledTestsAPI - ${methodName}: Error fetching scheduled tests for user UUID: ${userUUID} - ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to find scheduled tests for user',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findAllActiveTests() {
    const methodName = 'findAllActiveTests';
    try {
      this.logger.log(
        `ScheduledTestsAPI - ${methodName}: Fetching all active tests`,
      );

      // Fetch and return all tests where the active flag is true
      const tests = await this.database.test.findMany({
        where: { active: true },
        select: {
          id: true,
          name: true,
          created_at: true,
          updated_at: true,
          is_mandatory: true,
          notice_period: true,
          color: true,
        },
      });
      this.logger.log(
        `ScheduledTestsAPI - ${methodName}: Successfully fetched all active tests`,
      );
      return tests;
    } catch (error) {
      this.logger.error(
        `ScheduledTestsAPI - ${methodName}: Error fetching all active tests - ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to fetch all active tests',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async initiateTestsTemplate(userUUID: string): Promise<{ success: boolean }> {
    const methodName = 'initiateTestsTemplate';
    try {
      this.logger.log(
        `ScheduledTestsAPI - ${methodName}: Initiating tests template for user UUID: ${userUUID}`,
      );
      const currentDate = moment();
      const trainingDuration = 84;

      const template = [
        { test_id: 2, start_day: 0, repetitive: false },
        { test_id: 1, start_day: 0, repetitive: true, interval: 84 },
        { test_id: 13, start_day: 0, repetitive: true, interval: 42 },
      ];

      const user = await this.database.user.upsert({
        where: { uuid: userUUID },
        create: {
          uuid: userUUID,
          registered_on: new Date(),
          starred: false,
          type: 'standard',
          deleted: false,
        },
        update: {},
      });

      if (!user) {
        throw new Error('User not found');
      }

      for (const item of template) {
        const startDate = moment(currentDate).add(item.start_day, 'days');
        const numOfIntervals = item.repetitive
          ? Math.floor(trainingDuration / item.interval)
          : 0;
        const numOfTests = 1 + numOfIntervals;

        for (let i = 0; i < numOfTests; i++) {
          const testDate = moment(startDate).add(i * item.interval, 'days');

          if (
            testDate.diff(
              moment(currentDate).add(trainingDuration, 'days'),
              'days',
            ) <= 0
          ) {
            await this.database.scheduled_test.create({
              data: {
                user_id: user.id,
                test_id: item.test_id,
                scheduled_datetime: testDate.toDate(),
                is_mandatory: true,
                notice_period: 5,
                created_at: new Date(),
                updated_at: new Date(),
              },
            });
          }
        }
      }
      this.logger.log(
        `ScheduledTestsAPI - ${methodName}: Successfully initiated tests template for user UUID: ${userUUID}`,
      );
      return { success: true };
    } catch (error) {
      this.logger.error(
        `ScheduledTestsAPI - ${methodName}: Error initiating tests template for user UUID: ${userUUID} - ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to initiate tests template',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
