import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsBoolean, IsInt, IsNotEmpty } from 'class-validator';

export class CreateScheduledTestDto {
  @ApiProperty({
    required: true,
    description: 'The unique identifier of the test',
  })
  @IsInt()
  @IsNotEmpty()
  testID: number;

  @ApiProperty({
    required: true,
    description: 'The scheduled date and time for the test',
  })
  @IsDateString()
  @IsNotEmpty()
  scheduledDateTime: string;

  @ApiProperty({ required: true, description: 'Whether the test is mandatory' })
  @IsBoolean()
  isMandatory: boolean;

  @ApiProperty({
    required: true,
    description: 'The notice period before the test',
  })
  @IsInt()
  noticePeriod: number;
}
