import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsDateString } from 'class-validator';

export class GetScheduledTestsQueryDto {
  @ApiProperty({
    required: false,
    description:
      'The start date time from which scheduled tests should be fetched. Use ISO 8601 format.',
  })
  @IsOptional()
  @IsDateString()
  startDateTime?: string;

  @ApiProperty({
    required: false,
    description:
      'The end date time until which scheduled tests should be fetched. Use ISO 8601 format.',
  })
  @IsOptional()
  @IsDateString()
  endDateTime?: string;
}
