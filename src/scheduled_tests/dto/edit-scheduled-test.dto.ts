import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsBoolean, IsNumber, IsOptional } from 'class-validator';

export class EditScheduledTestDto {
  @ApiProperty({
    required: false,
    description: 'The new scheduled date and time for the test.',
  })
  @IsOptional()
  @IsDateString()
  scheduledDateTime?: string;

  @ApiProperty({
    required: false,
    description:
      'The identifier of the test associated with this scheduled test.',
  })
  @IsOptional()
  @IsNumber()
  testID?: number;

  @ApiProperty({
    required: false,
    description: 'Specifies whether the test is mandatory.',
  })
  @IsOptional()
  @IsBoolean()
  isMandatory?: boolean;

  @ApiProperty({
    required: false,
    description: 'The notice period (in days) before the test occurs.',
  })
  @IsOptional()
  @IsNumber()
  noticePeriod?: number;

  @ApiProperty({
    required: false,
    description:
      'The completion date and time for the test, if it has been completed.',
  })
  @IsOptional()
  @IsDateString()
  completionDateTime?: string;
}
