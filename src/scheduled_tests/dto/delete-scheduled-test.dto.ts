import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsUUID, IsInt, Min } from 'class-validator';

export class DeleteScheduledTestDto {
  @ApiProperty({ description: 'UUID of the user', required: true })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  userUUID: string;

  @ApiProperty({
    description: 'ID of the scheduled test to delete',
    required: true,
    type: 'integer',
  })
  @IsInt()
  @IsNotEmpty()
  scheduledTestID: number;
}
