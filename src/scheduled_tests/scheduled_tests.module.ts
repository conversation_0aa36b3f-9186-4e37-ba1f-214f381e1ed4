import { Module } from '@nestjs/common';
import { WinstonModule } from 'nest-winston';
import * as winston from 'winston'; // Make sure to import winston
import { transports, format } from 'winston';
import { ScheduledTestsController } from './scheduled_tests.controller';
import { ScheduledTestsService } from './scheduled_tests.service';

@Module({
  imports: [],
  controllers: [ScheduledTestsController],
  providers: [ScheduledTestsService],
})
export class ScheduledTestsModule {}
