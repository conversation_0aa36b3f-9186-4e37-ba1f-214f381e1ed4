import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Res,
} from '@nestjs/common';
import * as moment from 'moment';
import {
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Response } from 'express';
import { ScheduledTestsService } from './scheduled_tests.service';
import { CreateScheduledTestDto } from './dto/create-scheduled-test.dto';
import { GetScheduledTestsQueryDto } from './dto/get-scheduled-test.dto';
import { EditScheduledTestDto } from './dto/edit-scheduled-test.dto';
@ApiTags('scheduled_tests')
@Controller('scheduled_tests')
export class ScheduledTestsController {
  constructor(private scheduledTestsService: ScheduledTestsService) {}

  @Post('/:userUUID/scheduled-tests')
  @ApiOperation({
    summary: 'Add a new scheduled test',
    description:
      'Creates a new scheduled test for the specified user based on the provided test details.',
  })
  @ApiParam({
    name: 'userUUID',
    description: 'The UUID of the user for whom the test is being scheduled',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'The scheduled test has been successfully created.',
  })
  @ApiResponse({ status: 404, description: 'User not found.' })
  @ApiResponse({ status: 500, description: 'Internal server error.' })
  async addScheduledTest(
    @Param('userUUID') userUUID: string,
    @Body() createScheduledTestDto: CreateScheduledTestDto,
    @Res() res: Response,
  ) {
    try {
      const result = await this.scheduledTestsService.addScheduledTest(
        userUUID,
        createScheduledTestDto,
      );
      res.status(HttpStatus.OK).json(result);
    } catch (error) {
      res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ error: error.message });
    }
  }

  @Delete('/:userUUID/scheduled-tests/:scheduledTestID')
  @ApiOperation({
    summary: 'Delete a scheduled test',
    description:
      'Deletes a specific scheduled test for a user identified by userUUID and scheduledTestID.',
  })
  @ApiParam({
    name: 'userUUID',
    description: 'UUID of the user associated with the test',
    type: String,
  })
  @ApiParam({
    name: 'scheduledTestID',
    description: 'ID of the scheduled test to delete',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'Scheduled test successfully deleted.',
  })
  @ApiResponse({
    status: 404,
    description: 'User or scheduled test not found.',
  })
  @ApiResponse({
    status: 500,
    description:
      'Internal server error encountered while attempting to delete the scheduled test.',
  })
  async deleteScheduledTest(
    @Param('userUUID') userUUID: string,
    @Param('scheduledTestID', ParseIntPipe) scheduledTestID: number,
    @Res() res: Response,
  ) {
    try {
      const result = await this.scheduledTestsService.deleteScheduledTest(
        userUUID,
        scheduledTestID,
      );
      res.json(result);
    } catch (error) {
      res
        .status(error.status || HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ error: error.message });
    }
  }

  @Get('/:userUUID/get-scheduled-tests')
  @ApiOperation({
    summary: 'Get all the scheduled tests for a user',
    description:
      'Retrieves all scheduled tests for a specific user, optionally filtered by date range.',
  })
  @ApiParam({
    name: 'userUUID',
    description: 'UUID of the user to retrieve scheduled tests for',
    type: String,
  })
  @ApiQuery({
    name: 'startDateTime',
    description: 'Start date for filtering scheduled tests',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'endDateTime',
    description: 'End date for filtering scheduled tests',
    required: false,
    type: String,
  })
  async getScheduledTestsForUser(
    @Param('userUUID') userUUID: string,
    @Query() query: GetScheduledTestsQueryDto,
    @Res() res: Response,
  ) {
    try {
      const tests = await this.scheduledTestsService.findScheduledTestsForUser(
        userUUID,
        query,
      );
      res.json(tests);
    } catch (error) {
      res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ error: error.message });
    }
  }

  @Get('/getTests')
  @ApiOperation({
    summary: 'Get available tests',
    description:
      'Retrieves all tests that are currently marked as active within the system.',
  })
  async getTests(@Res() res: Response) {
    try {
      const tests = await this.scheduledTestsService.findAllActiveTests();
      res.json(tests);
    } catch (error) {
      res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ error: error.message });
    }
  }

  @Put('/:userUUID/scheduled-tests/:scheduledTestID')
  @ApiOperation({
    summary: 'Edit scheduled tests',
    description:
      'Updates the details of an existing scheduled test for a specific user.',
  })
  @ApiParam({
    name: 'userUUID',
    description: 'UUID of the user associated with the scheduled test',
    type: String,
  })
  @ApiParam({
    name: 'scheduledTestID',
    description: 'ID of the scheduled test to update',
    type: Number,
  })
  @ApiBody({
    type: EditScheduledTestDto,
    description: 'Payload containing updated test details',
  })
  async editScheduledTest(
    @Param('userUUID') userUUID: string,
    @Param('scheduledTestID', ParseIntPipe) scheduledTestID: number,
    @Body() dto: EditScheduledTestDto,
    @Res() res: Response,
  ) {
    try {
      const updatedTest = await this.scheduledTestsService.updateScheduledTest(
        userUUID,
        scheduledTestID,
        dto,
      );
      res.json(updatedTest);
    } catch (error) {
      res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ error: error.message });
    }
  }

  @Post('/:userUUID/initiate-tests-template')
  async initiateTestsTemplate(@Param('userUUID') userUUID: string) {
    try {
      const result =
        await this.scheduledTestsService.initiateTestsTemplate(userUUID);
      return result;
    } catch (error) {
      throw new HttpException(
        'Failed to process request',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
