import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, <PERSON><PERSON>ength, MaxLength } from 'class-validator';

export class RegisterUserDto {
  @ApiProperty({
    required: true,
    description:
      'The username for the new user account. Must be unique and between 4 to 20 characters.',
    minLength: 4,
    maxLength: 20,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(4)
  @MaxLength(20)
  username: string;

  @ApiProperty({
    required: true,
    description:
      'The password for the new user account. Must be at least 4 characters.',
    minLength: 4,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(4)
  password: string;

  @ApiProperty({
    required: false,
    description:
      'Optional date when the user account was added, represented as a Unix timestamp string. If not provided, the current date and time are used.',
    type: 'string',
  })
  dateAdded?: string;
}
