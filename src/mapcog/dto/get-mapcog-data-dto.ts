import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsDateString } from 'class-validator';

export class GetMapcogDataDto {
  @ApiProperty({
    description: 'Optional filter parameter for specifying a start date.',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({
    description: 'Optional filter parameter for specifying an end date.',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;
}
