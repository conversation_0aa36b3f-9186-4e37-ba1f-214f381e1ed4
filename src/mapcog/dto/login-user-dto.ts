import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class LoginUserDto {
  @ApiProperty({
    required: true,
    description: 'The username of the user attempting to log in.',
  })
  @IsString()
  @IsNotEmpty()
  username: string;

  @ApiProperty({
    required: true,
    description: 'The password of the user attempting to log in.',
  })
  @IsString()
  @IsNotEmpty()
  password: string;
}
