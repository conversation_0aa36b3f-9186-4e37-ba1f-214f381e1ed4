import {
  Body,
  Controller,
  HttpException,
  HttpStatus,
  Post,
  Res,
} from '@nestjs/common';
import { MapcogService } from './mapcog.service';
import { UserFilterOptions } from './mapcog.interface';
import * as moment from 'moment';
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { RegisterUserDto } from './dto/register-user-dto';
import { Response } from 'express';
import { LoginUserDto } from './dto/login-user-dto';
@ApiTags('mapcog')
@Controller('mapcog')
export class MapcogController {
  constructor(private mapcogService: MapcogService) {}

  @Post('fetchMapcog')
  @ApiOperation({
    summary: 'Fetch Mapcog Data',
    description:
      'Retrieves patients and tests data associated with a given legpers identifier.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Patients and tests data successfully retrieved.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Error fetching mapcog data.',
  })
  async fetchMapcogData(@Body('legpers') legpers: string, @Res() res) {
    try {
      const patients = await this.mapcogService.fetchPatients(legpers);
      const tests = await this.mapcogService.fetchTests(legpers);

      const responseData = { patients, tests };
      return res.status(HttpStatus.OK).json(responseData);
    } catch (error) {
      console.error('Error fetching mapcog data:', error);
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).send(error);
    }
  }

  @Post('/login')
  @ApiOperation({
    summary: 'Log in a user',
    description: 'Authenticates a user by username and password.',
  })
  @ApiBody({
    description: 'Login credentials',
    type: LoginUserDto,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User successfully logged in.',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid credentials.',
  })
  async login(@Body() loginUserDto: LoginUserDto, @Res() res) {
    const { username, password } = loginUserDto;

    const filterOptions: UserFilterOptions = { username, password };

    if (await this.mapcogService.findUser(filterOptions)) {
      return res.status(HttpStatus.OK).send('OK');
    } else {
      return res.status(HttpStatus.UNAUTHORIZED).send('Invalid credentials');
    }
  }

  @Post('/web-login')
  @ApiOperation({
    summary: 'Log in a user in the web portal',
    description: 'Authenticates a user by username and password.',
  })
  @ApiBody({
    description: 'Login credentials',
    type: LoginUserDto,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User successfully logged in.',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid credentials.',
  })
  async webLogin(@Body() loginUserDto: LoginUserDto, @Res() res) {
    const { username, password } = loginUserDto;

    const filterOptions: UserFilterOptions = { username, password };

    let mapcogUser = await this.mapcogService.findUser(filterOptions);
    if (mapcogUser) {
      return res.status(HttpStatus.OK).send({ logindef: mapcogUser.logindef });
    } else {
      return res.status(HttpStatus.UNAUTHORIZED).send('Invalid credentials');
    }
  }

  @Post('/register')
  @ApiOperation({
    summary: 'Register a new user',
    description:
      'Registers a new user with a username and password. Checks if the username already exists.',
  })
  @ApiBody({
    description: 'User registration details',
    type: RegisterUserDto,
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'User successfully registered.',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Username already exists.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Failed registration due to internal server error.',
  })
  async register(
    @Body() registerUserDto: RegisterUserDto,
    @Res() res: Response,
  ) {
    const { username, password } = registerUserDto;
    const filterOptions: UserFilterOptions = { username };
    const userExists = await this.mapcogService.findUser(filterOptions);

    if (userExists) {
      return res.status(HttpStatus.CONFLICT).send('Username already exists');
    }
    try {
      const dateAdded = moment().unix().toString();
      const result = await this.mapcogService.createUser({
        username,
        password,
        dateAdded,
      });

      return res.status(HttpStatus.CREATED).send(result);
    } catch (error) {
      console.error('Failed registration with error:', error);
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .send('Internal server error');
    }
  }

  @Post('/getMapcogData')
  @ApiOperation({
    summary: 'Process Mapcog Data',
    description:
      'Inserts Mapcog data provided in the request body into the database.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Mapcog data successfully processed and inserted.',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Error processing Mapcog data.',
  })
  async processMapcogData(@Body() requestBody: any, @Res() res) {
    try {
      const result = await this.mapcogService.insertMapcogData(requestBody);
      res.status(HttpStatus.OK).send(result);
    } catch (error) {
      // Log the error
      console.error('Error processing data:', error);

      // Send a 500 internal server error response
      throw new HttpException(
        'Internal Server Error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
