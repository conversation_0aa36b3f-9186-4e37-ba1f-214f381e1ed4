import { Body, Injectable, Inject, Res, Logger } from '@nestjs/common';
import { DatabaseService } from 'src/database/database.service';
import { UserCreationData, UserFilterOptions } from './mapcog.interface';
import { mapcog_legPers } from '@prisma/client';
import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
@Injectable({})
export class MapcogService {
  private readonly logger = new Logger(MapcogService.name);
  constructor(private database: DatabaseService) {}

  async fetchPatients(legPersId: string) {
    try {
      return await this.database.mapcog_patient.findMany({
        where: { legPersId: legPersId },
      });
    } catch (error) {
      console.error('Error fetching patients:', error);
      throw error;
    }
  }

  async fetchTests(legPersId: string) {
    try {
      return await this.database.mapcog_test.findMany({
        where: { legPersId: legPersId },
        include: {
          results: {
            include: {
              pauses: true,
            },
          },
        },
      });
    } catch (error) {
      console.error('Error fetching tests:', error);
      throw error;
    }
  }
  async fetchAllPatients() {
    try {
      const patients = await this.database.mapcog_patient.findMany();
      return patients;
    } catch (error) {
      console.error('Error fetching patients:', error);
      throw error;
    }
  }
  private decodeUrl(encodedString: string): string {
    return decodeURIComponent(encodedString);
  }

  private splitMain(data: string): string[] {
    return this.decodeUrl(data).split(';!').filter(Boolean);
  }

  private splitPauses(data: string): string[][] {
    let data1 = this.decodeUrl(data).split(';!');
    return data1.map((chunk) => this.decodeUrl(chunk).split(','));
  }

  private getDecimalValue(number: number): number {
    const decimals = String(number).split('.')[1] || '';
    return Number(decimals);
  }
  async insertMapcogData(requestBody: any): Promise<string> {
    const {
      legpers, //legpers id
      pids, //patients ids
      pdates, //patient added date
      firstName,
      lastName,
      pbds, //patients age
      psex, //patients sex
      plpid, //legpers ids for each test
      tlpids,
      tpids, //patient ids for tests
      tdates, //test dates
      tmeds, //medications
      tdiags, //diagnosis
      tcomms, //comments
      tr1durs, // first test durations
      tr1pauses, //first test pauses
      tr2durs, // second test durations
      tr2pauses, // second test pauses
      tr3durs, //not used
      tr3pauses, //not used
    } = requestBody;
    this.logger.log('MapcogService - Starting data insertion process.');

    console.time('Data Splitting');
    const tdatesArr = this.splitMain(tdates);
    const tpidsArr = this.splitMain(tpids);
    const firstNameArr = this.splitMain(firstName);
    const lastNameArr = this.splitMain(lastName);
    const tlpidsArr = this.splitMain(tlpids);
    const tmedsArr = this.splitMain(tmeds);
    const tdiagsArr = this.splitMain(tdiags);
    const tcommsArr = this.splitMain(tcomms);
    const plpidArr = this.splitMain(plpid);
    const pidsArr = this.splitMain(pids);
    const pbdsArr = this.splitMain(pbds);
    const psexArr = this.splitMain(psex);
    const pdatesArr = this.splitMain(pdates);
    const tr1dursArr = this.splitMain(tr1durs);
    const tr1pausesArr = this.splitPauses(tr1pauses);
    const tr2dursArr = this.splitMain(tr2durs);
    const tr2pausesArr = this.splitPauses(tr2pauses);
    const tr3dursArr = this.splitMain(tr3durs);
    const tr3pausesArr = this.splitPauses(tr3pauses);
    console.timeEnd('Data Splitting');

    // Check for patient
    console.time('Patient Check Loop');

    // Initialize an array to hold new patient data and a map for patient inclusion
    const newPatients = [];
    const processedPatients = new Map();

    // Loop through each patient ID in the provided array
    for (let i = 0; i < pidsArr.length; i++) {
      if (isNaN(parseInt(pbdsArr[i], 10))) {
        continue;
      }

      // Construct a unique identifier for each patient
      const patientIdentifier = `${pidsArr[i]}-${plpidArr[i]}`;

      // Check if this patient has already been processed
      if (processedPatients.get(patientIdentifier)) {
        console.log(
          `Patient with patientId ${pidsArr[i]} and legPersId ${plpidArr[i]} is a duplicate in the array. Skipping.`,
        );
        continue;
      }

      // Retrieve all patients from the database with the current patient ID
      const existingPatients = await this.database.mapcog_patient.findMany({
        where: { patientId: pidsArr[i] },
      });

      // Flag to check if a patient already exists in the database
      let patientExists = false;

      // Iterate over each retrieved patient
      for (const patient of existingPatients) {
        // Check if any existing patient has the same legPersId as the current one
        if (patient.legPersId == plpidArr[i]) {
          patientExists = true;
          break;
        }
      }

      // If a patient with the same ID and legPersId already exists, log it and skip to the next iteration
      if (patientExists) {
        continue;
      }

      // Mark this patient as processed in the Map
      processedPatients.set(patientIdentifier, true);

      // If no matching patient is found, add the new patient data to the array
      newPatients.push({
        sent: null,
        legPersId: plpidArr[i].replace(/^,|,$/g, '').trim(),
        patientId: pidsArr[i].replace(/^,|,$/g, '').trim(),
        firstName: firstNameArr[i],
        lastName: lastNameArr[i],
        age: parseInt(pbdsArr[i], 10), // Parsing age to an integer
        gender: psexArr[i].replace(/^,|,$/g, '').trim(),
        dateAdded: pdatesArr[i].replace(/^,|,$/g, '').trim(),
      });
    }

    // Batch insert the new patients into the database
    await this.database.mapcog_patient.createMany({ data: newPatients });

    console.timeEnd('Patient Check Loop');

    console.time('Test Insertion Loop');

    // Function to process the length of a pause and return its time and length values.
    const processPause = (pauseLength: number) => {
      const pauseTime = isNaN(Math.floor(pauseLength))
        ? 0
        : Math.floor(pauseLength);
      const pauseLengthValue = this.getDecimalValue(pauseLength);
      return {
        pauseTime: pauseTime,
        pauseLength: isNaN(pauseLengthValue) ? 0 : pauseLengthValue,
      };
    };

    // Initialize an array to store pause data for each date.
    const pausesArray = new Array(tdatesArr.length);

    // Function to process pauses in two arrays and update the pausesArray at a specific index.
    const processPausesInOneLoop = async (
      arr1: string[],
      arr2: string[],
      index: number,
    ) => {
      return new Promise<void>((resolve) => {
        arr1 = Array.isArray(arr1) ? arr1 : [];
        arr2 = Array.isArray(arr2) ? arr2 : [];

        let maxLength = Math.max(arr1.length, arr2.length);
        let combinedPauses = {
          pausesForFirstResult: [],
          pausesForSecondResult: [],
        };

        for (let j = 0; j < maxLength; j++) {
          if (j < arr1.length) {
            combinedPauses.pausesForFirstResult.push(
              processPause(Number(arr1[j])),
            );
          }
          if (j < arr2.length) {
            combinedPauses.pausesForSecondResult.push(
              processPause(Number(arr2[j])),
            );
          }
        }

        pausesArray[index] = combinedPauses;
        resolve();
      });
    };

    // Queue to store promises for processing pauses.
    const operationsQueue = [];

    // Loop through dates to process pauses and add them to the operations queue.
    for (let i = 0; i < tdatesArr.length; i++) {
      operationsQueue.push(
        processPausesInOneLoop(tr1pausesArr[i], tr2pausesArr[i], i),
      );
    }

    console.time(`operations queue duration`);
    // Execute all promises in the operations queue and measure time taken.
    await Promise.all(operationsQueue);
    console.timeEnd(`operations queue duration`);
    // Async function to process data and insert into the database.
    const processDataAndInsert = async (
      i: number,
      duration1: number,
      duration2: number,
      duration3: number,
      pausesForFirstResult: any[],
      pausesForSecondResult: any[],
    ) => {
      let data = {
        sent: null,
        dateAdded: tdatesArr[i].replace(/^,|,$/g, '').trim(),
        mapcog_legPers: {
          connect: {
            username: tlpidsArr[i].replace(/^,|,$/g, '').trim(),
          },
        },
        mapcog_patient: {
          connect: {
            patientId: tpidsArr[i].replace(/^,|,$/g, '').trim(),
          },
        },
        medicine: tmedsArr[i],
        dose: null,
        diagnosis: tdiagsArr[i],
        comments: tcommsArr[i],
        results: {
          create: [
            {
              orderIndex: 0,
              duration: duration1,
              pauses: {
                create: pausesForFirstResult,
              },
            },
            {
              orderIndex: 1,
              duration: duration2,
              pauses: {
                create: pausesForSecondResult,
              },
            },
            {
              orderIndex: 2,
              duration: duration3,
            },
          ],
        },
      };
      try {
        await this.database.mapcog_test.create({ data: data });
      } catch (error) {
        this.logger.error(
          `MapcogService - Error in data insertion process - ${error.message}`,
          error.stack,
        );
        throw new Error('Failed to insert data');
      }
    };
    // Array to store data insertion operations.
    const insertData = [];

    // Loop through dates, process data, and prepare for insertion.
    for (const [i, value] of tdatesArr.entries()) {
      let { pausesForFirstResult, pausesForSecondResult } = pausesArray[i];

      const duration1 = parseInt(
        tr1dursArr[i].replace(/^,|,$/g, '').trim(),
        10,
      );
      const duration2 = parseInt(
        tr2dursArr[i].replace(/^,|,$/g, '').trim(),
        10,
      );
      const duration3 = parseInt(
        tr3dursArr[i].replace(/^,|,$/g, '').trim(),
        10,
      );

      if (!isNaN(duration1) && !isNaN(duration2)) {
        insertData.push(
          processDataAndInsert(
            i,
            duration1,
            duration2,
            duration3,
            pausesForFirstResult,
            pausesForSecondResult,
          ),
        );
      } else {
        this.logger.error(
          `Invalid durations for index ${i}: duration1 - ${tr1dursArr[i]}, duration2 - ${tr2dursArr[i]}`,
        );
      }
    }

    console.time(`insert data duration`);
    // Execute all data insertion operations.
    await Promise.all(insertData);
    console.timeEnd(`insert data duration`);

    console.timeEnd('Test Insertion Loop');
    this.logger.log('MapcogService - Data inserted successfully.');
    return 'Data inserted successfully';
  }

  async findUser(
    filterOptions: UserFilterOptions,
  ): Promise<mapcog_legPers | null> {
    try {
      this.logger.log('MapcogService - Finding user');
      const user = await this.database.mapcog_legPers.findFirst({
        where: filterOptions,
      });
      if (!user) {
        this.logger.warn(
          'MapcogService - No user found with given filter options',
        );
      } else {
        this.logger.log('MapcogService - User found');
      }
      return user;
    } catch (error) {
      this.logger.error(
        `MapcogService - Failed to find user with error: ${error.message}`,
        error.stack,
      );
      throw new Error('Internal server error while finding user');
    }
  }

  async createUser(userData: UserCreationData): Promise<string> {
    try {
      this.logger.log('MapcogService - Registering new user');
      await this.database.mapcog_legPers.create({ data: userData });
      this.logger.log('MapcogService - User registered successfully');
      return 'User registered successfully';
    } catch (error) {
      this.logger.error(
        `MapcogService - Failed to register user - ${error.message}`,
        error.stack,
      );
      throw new Error('Internal server error while registering user');
    }
  }
}
