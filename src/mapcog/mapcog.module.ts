import { Module } from '@nestjs/common';
import { MapcogController } from './mapcog.controller';
import { MapcogService } from './mapcog.service';
import { WinstonModule } from 'nest-winston';
import * as winston from 'winston'; // Make sure to import winston
import { transports, format } from 'winston';

@Module({
  imports: [],
  controllers: [MapcogController],
  providers: [MapcogService],
})
export class MapcogModule {}
