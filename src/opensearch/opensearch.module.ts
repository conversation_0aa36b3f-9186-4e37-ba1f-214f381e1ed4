import { Modu<PERSON> } from '@nestjs/common';
import { OpensearchModule } from 'nestjs-opensearch';
import { LogsController } from './opensearch.controller';
import { LoggingService } from './opensearch.service';

@Module({
  imports: [
    OpensearchModule.forRoot({
      node: 'http://35.240.48.115:9200',
      auth: {
        username: 'admin',
        password: 'admin',
      },
      ssl: {
        rejectUnauthorized: false,
      },
    }),
  ],
  controllers: [LogsController],
  providers: [LoggingService],
})
export class LoggerModule {}
