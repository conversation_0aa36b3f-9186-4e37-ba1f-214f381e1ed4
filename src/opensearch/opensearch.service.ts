import { Injectable, Inject } from '@nestjs/common';
import { InjectOpensearchClient, OpensearchClient } from 'nestjs-opensearch';

@Injectable()
export class LoggingService {
  constructor(
    @InjectOpensearchClient() private readonly os: OpensearchClient,
  ) {}

  async log(key: string, level: string, message: string, meta: any = {}) {
    const index = `app-logs-${new Date().toISOString().slice(0, 10)}`;
    await this.os.index({
      index,
      body: {
        '@timestamp': new Date().toISOString(),
        key,
        level,
        message,
        meta,
      },
    });
  }
}
