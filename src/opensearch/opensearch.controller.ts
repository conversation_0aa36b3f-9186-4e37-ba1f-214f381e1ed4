import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  DefaultValuePipe,
  HttpCode,
  HttpStatus,
  ParseIntPipe,
} from '@nestjs/common';
import { InjectOpensearchClient, OpensearchClient } from 'nestjs-opensearch';

interface LogEntry {
  key: string;
  level: string;
  message: string;
  meta?: Record<string, any>;
}

@Controller('logs')
export class LogsController {
  constructor(
    @InjectOpensearchClient() private readonly os: OpensearchClient,
  ) {}

  @Post('bulk')
  @HttpCode(200)
  async bulkCreate(@Body() batch: LogEntry[]) {
    const indexName = `app-logs-${new Date().toISOString().slice(0, 10)}`;

    // Build a single bulk request
    const body = batch.flatMap((entry) => [
      { index: { _index: indexName } },
      {
        '@timestamp': new Date().toISOString(),
        key: entry.key, // <-- include the user key here
        level: entry.level,
        message: entry.message,
        meta: entry.meta || {},
      },
    ]);

    const { body: bulkResp } = await this.os.bulk({ refresh: true, body });

    if (bulkResp.errors) {
      const errors = bulkResp.items
        .map((item: any) => item.index?.error)
        .filter((e: any) => !!e);
      return { success: false, errors };
    }

    return { success: true, count: batch.length };
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(
    @Body('key') key: string,
    @Body('level') level: string,
    @Body('message') message: string,
    @Body('meta', new DefaultValuePipe({})) meta: Record<string, any>,
  ) {
    const indexName = `app-logs-${new Date().toISOString().slice(0, 10)}`;
    const now = new Date().toISOString();

    const resp = await this.os.index({
      index: indexName,
      body: {
        '@timestamp': now,
        key,
        level,
        message,
        meta,
      },
    });

    await this.os.indices.refresh({ index: indexName });

    return {
      result: resp.body.result,
      _id: resp.body._id,
      index: resp.body._index,
    };
  }

  @Get()
  async search(
    @Query('q') q?: string,
    @Query('key') key?: string,
    @Query('level') level?: string,
    @Query('from', new DefaultValuePipe(0), ParseIntPipe) from: number = 0,
    @Query('size', new DefaultValuePipe(50), ParseIntPipe) size: number = 50,
  ) {
    const must: any[] = [];

    // Full-text search on "message"
    if (q) {
      must.push({ multi_match: { query: q, fields: ['message'] } });
    }

    // Exact match on "level"
    if (level) {
      must.push({ term: { level } });
    }

    // Exact match on "key" (user UUID)
    if (key) {
      must.push({ term: { key } });
    }
    const { body } = await this.os.search({
      index: 'app-logs-*',
      from,
      size,
      body: {
        query: must.length ? { bool: { must } } : { match_all: {} },
        sort: [{ '@timestamp': { order: 'desc' } }],
        track_total_hits: true,
      },
    });
    const hits = (body.hits.hits as any[]).map((h) => h._source);
    const totalRaw = body.hits.total;
    const total = typeof totalRaw === 'number' ? totalRaw : totalRaw.value;

    return { items: hits, total, from, size };
  }
}
