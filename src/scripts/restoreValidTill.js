import admin from 'firebase-admin';
import { Firestore } from '@google-cloud/firestore';

async function main() {
  const firstApp = admin.initializeApp(
    {
      credential: admin.credential.cert('./firebaseServiceAccount.json'),
    },
    'firstApp',
  );
  const firstFirestore = admin.firestore(firstApp);

  const backupFirestore = new Firestore({
    projectId: 'imvi-read-stage',
    databaseId: 'backup',
    keyFilename: './firebaseServiceAccount2.json',
  });

  // Retrieve all user documents from Project 1
  const usersSnapshot = await firstFirestore.collection('UserData').get();

  for (const userDoc of usersSnapshot.docs) {
    const userId = userDoc.id;
    console.log(`\nProcessing user: ${userId}`);

    // Define document references for both projects
    const docRefFirst = firstFirestore.collection('UserData').doc(userId);
    const docRefBackup = backupFirestore.collection('UserData').doc(userId);

    const snapshotBackup = await docRefBackup.get();

    if (!snapshotBackup.exists) {
      console.log(
        `Project 2 (Backup) document for user ${userId} does not exist. Skipping update.`,
      );
      continue;
    }

    const backupData = snapshotBackup.data();

    if (backupData && backupData.ValidTill) {
      try {
        await docRefFirst.update({
          ValidTill: backupData.ValidTill,
        });
        console.log(
          `Updated Project 1 for user ${userId} with ValidTill: ${backupData.ValidTill}`,
        );
      } catch (error) {
        console.error(`Error updating user ${userId}:`, error);
      }
    } else {
      console.log(
        `No ValidTill field found in backup for user ${userId}. Skipping update.`,
      );
    }
  }
}

main().catch(console.error);
