import admin from 'firebase-admin';
import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables from the appropriate .env file
dotenv.config({ path: `.env.${process.env.NODE_ENV || 'local'}` });
console.log('Environment set to:', process.env.NODE_ENV);

// Initialize Firebase Admin SDK
const serviceAccount = JSON.parse(
  process.env.FIREBASE_SERVICE_ACCOUNT_JSON || '{}',
);
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});

const firestore = admin.firestore();

const migrateCalibrationData = async () => {
  try {
    console.log('Starting calibration data migration...');

    const calibrateResultSnapshot = await firestore
      .collection('CalibrateResult')
      .get();

    for (const doc of calibrateResultSnapshot.docs) {
      const calibrateResultData = doc.data();
      const userRefPath = calibrateResultData.User?.path;

      if (!userRefPath) {
        console.log('User reference not found, skipping document:', doc.id);
        continue;
      }

      const userId = userRefPath.split('/').pop();
      console.log('Processing user:', userId);

      const calibrationArray = calibrateResultData.CalibrationData || [];

      if (!Array.isArray(calibrationArray) || calibrationArray.length === 0) {
        console.log('Calibration data is empty, skipping:', doc.id);
        continue;
      }

      const calibratedOnDate = calibrateResultData.CalibratedOnDate?.toDate();

      if (!calibratedOnDate || isNaN(calibratedOnDate.getTime())) {
        console.log('Invalid CalibratedOnDate, skipping entry.');
        continue;
      }

      const datetime = calibratedOnDate.toISOString();

      const calibrationData = {
        array: calibrationArray,
        datetime,
      };

      console.log('Sending calibration data:', calibrationData);

      try {
        const response = await axios.put(
          `http://localhost:3002/user/${userId}/calibration_data`,
          calibrationData,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        );

        console.log('API response:', response.data);
      } catch (apiError) {
        console.error(
          'Error updating calibration data via API for user:',
          userId,
          apiError.message,
        );
      }
    }

    console.log('Calibration data migration completed.');
  } catch (error) {
    console.error('Migration failed:', error);
  }
};

migrateCalibrationData();
