import admin from 'firebase-admin';
import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: `.env.${process.env.NODE_ENV || 'local'}` });
console.log('Environment set to:', process.env.NODE_ENV);

// Initialize Firebase Admin SDK
const serviceAccount = JSON.parse(
  process.env.FIREBASE_SERVICE_ACCOUNT_JSON || '{}',
);
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});

const firestore = admin.firestore();

const migrateChangeFlagData = async () => {
  try {
    console.log('Starting ChangeFlag data migration...');

    const usersSnapshot = await firestore.collection('UserData').get();

    for (const doc of usersSnapshot.docs) {
      const userData = doc.data();
      const changeFlag = userData.ChangeFlag;

      if (changeFlag === undefined || changeFlag === null) {
        console.log(`ChangeFlag not found for user: ${doc.id}, skipping...`);
        continue;
      }

      console.log(`Processing user: ${doc.id}, ChangeFlag: ${changeFlag}`);

      try {
        const response = await axios.put(
          `http://localhost:3002/user/${doc.id}/update-field`,
          {
            field: 'change_flag',
            value: changeFlag,
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        );

        console.log(`API response for user ${doc.id}:`, response.data);
      } catch (apiError) {
        console.error(
          `Error updating ChangeFlag for user ${doc.id}:`,
          apiError.message,
        );
      }
    }

    console.log('ChangeFlag data migration completed.');
  } catch (error) {
    console.error('Migration failed:', error);
  }
};

migrateChangeFlagData();
