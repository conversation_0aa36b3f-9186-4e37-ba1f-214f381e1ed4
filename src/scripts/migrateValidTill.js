import admin from 'firebase-admin';
import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: `.env.${process.env.NODE_ENV || 'local'}` });
console.log('Environment set to:', process.env.NODE_ENV);

// Initialize Firebase Admin SDK
const serviceAccount = JSON.parse(
  process.env.FIREBASE_SERVICE_ACCOUNT_JSON || '{}',
);
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});

const firestore = admin.firestore();

const migrateValidUntilData = async () => {
  try {
    console.log('Starting ValidUntil data migration...');

    const usersSnapshot = await firestore.collection('UserData').get();

    for (const doc of usersSnapshot.docs) {
      const userData = doc.data();
      const validUntil = userData.ValidTill;

      if (!validUntil) {
        console.log(`ValidUntil not found for user: ${doc.id}, skipping...`);
        continue;
      }

      const validUntilDate = new Date(validUntil.toDate());
      if (isNaN(validUntilDate.getTime())) {
        console.log(`Invalid ValidUntil date for user: ${doc.id}, skipping...`);
        continue;
      }

      console.log(`Processing user: ${doc.id}, ValidUntil: ${validUntilDate}`);

      try {
        const response = await axios.put(
          `https://imvi-api-prod-li3wqfyhtq-nw.a.run.app/user/${doc.id}/update-field`,
          {
            field: 'valid_until',
            value: validUntilDate.toISOString(),
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        );

        console.log(`API response for user ${doc.id}:`, response.data);
      } catch (apiError) {
        console.error(
          `Error updating ValidUntil for user ${doc.id}:`,
          apiError.message,
        );
      }
    }

    console.log('ValidUntil data migration completed.');
  } catch (error) {
    console.error('Migration failed:', error);
  }
};

migrateValidUntilData();
