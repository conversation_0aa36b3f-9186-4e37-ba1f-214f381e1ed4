import admin from 'firebase-admin';
import axios from 'axios';

// Load environment variables
dotenv.config({ path: `.env.${process.env.NODE_ENV || 'local'}` });
console.log('Environment set to:', process.env.NODE_ENV);

// Initialize Firebase Admin SDK
const serviceAccount = JSON.parse(
  process.env.FIREBASE_SERVICE_ACCOUNT_JSON || '{}',
);
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});

const API_BASE_URL = 'https://imvi-api-dev-li3wqfyhtq-nw.a.run.app';

async function migrateDeviceData() {
  try {
    // Get all documents from the "UserData" collection.
    const snapshot = await firestore.collection('UserData').get();
    console.log(`Found ${snapshot.size} user documents in Firestore.`);

    // Loop through each user document.
    for (const doc of snapshot.docs) {
      const userUuid = doc.id; // The document ID is the user UUID.
      const data = doc.data();

      if (!data.DeviceInfo) {
        console.log(`User ${userUuid} does not have DeviceInfo.`);
        continue;
      }

      const deviceInfo = data.DeviceInfo;
      console.log(`DeviceInfo for user ${userUuid}:`, deviceInfo);

      if (!deviceInfo.deviceRef) {
        console.log(
          `User ${userUuid} does not have a deviceRef in DeviceInfo.`,
        );
        continue;
      }

      let deviceDocId;
      if (typeof deviceInfo.deviceRef === 'string') {
        const parts = deviceInfo.deviceRef.split('/');
        if (parts.length < 2) {
          console.log(
            `User ${userUuid} has an invalid deviceRef: ${deviceInfo.deviceRef}`,
          );
          continue;
        }
        deviceDocId = parts[1];
      } else if (
        deviceInfo.deviceRef &&
        typeof deviceInfo.deviceRef.id === 'string'
      ) {
        deviceDocId = deviceInfo.deviceRef.id;
      } else {
        console.log(`User ${userUuid} has an invalid deviceRef format.`);
        continue;
      }

      // Fetch the referenced device document from the "DeviceData" collection.
      const deviceDocSnap = await firestore
        .collection('DeviceData')
        .doc(deviceDocId)
        .get();
      if (!deviceDocSnap.exists) {
        console.log(
          `Device document ${deviceDocId} not found for user ${userUuid}`,
        );
        continue;
      }
      const deviceData = deviceDocSnap.data();
      if (!deviceData) {
        console.log(
          `No data found in device document ${deviceDocId} for user ${userUuid}`,
        );
        continue;
      }

      console.log(`User ${userUuid} device data:`, deviceData);

      // Build the update payload.
      const updateData = {
        deviceId: deviceData.deviceId,
        deviceOffset: deviceData.deviceOffset,
        diagonalLength:
          deviceData.diagonalLength ??
          (deviceData.dimensions?.diagonalLength || null),
        // Flatten dimensions if available:
        height: deviceData.dimensions?.height ?? null,
        pixelDensity: deviceData.dimensions?.pixelDensity ?? null,
        width: deviceData.dimensions?.width ?? null,
        mmfor1Pixel: deviceData.mmfor1Pixel,
        model: deviceData.model,
        os: deviceData.os,
        pixelfor1mm: deviceData.pixelfor1mm,
      };

      console.log(`Update payload for user ${userUuid}:`, updateData);

      try {
        // Call the update-device-data API endpoint for this user.
        const response = await axios.put(
          `${API_BASE_URL}/user/${userUuid}/update-device-data`,
          updateData,
          {
            headers: { 'Content-Type': 'application/json' },
          },
        );
        console.log(
          `Migration successful for user ${userUuid}:`,
          response.data,
        );
      } catch (apiError) {
        console.error(
          `Migration failed for user ${userUuid}:`,
          apiError.message,
        );
      }
    }
  } catch (error) {
    console.error('Migration error:', error);
  }
}

migrateDeviceData()
  .then(() => {
    console.log('Migration completed successfully.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration encountered an error:', error);
    process.exit(1);
  });
