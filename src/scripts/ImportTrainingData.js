import admin from 'firebase-admin';
import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: `.env.${process.env.NODE_ENV || 'local'}` });

console.log('Environment set to:', process.env.NODE_ENV || 'local');

// Parse the service account JSON from environment variable
const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_JSON);

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});

const firestore = admin.firestore();

const migrateData = async () => {
  try {
    console.log('Starting migration...');

    const userProgramSnapshot = await firestore.collection('UserProgram').get();

    for (const doc of userProgramSnapshot.docs) {
      const userProgramData = doc.data();
      const userRefPath = userProgramData.User?.path;

      if (!userRefPath) {
        console.log('User reference not found, skipping document:', doc.id);
        continue;
      }

      const userId = userRefPath.split('/').pop();
      console.log('Processing user:', userId);

      const sessionsDoneLog = userProgramData.SessionsDoneLog || [];

      for (const session of sessionsDoneLog) {
        const sessionName = session.SessionName || '';
        const sessionNumber = parseInt(sessionName.replace('Session_', ''), 10);

        if (isNaN(sessionNumber)) {
          console.log('Invalid session number, skipping:', sessionName);
          continue;
        }

        const multiSessionLog = session.MultiSessionlog || [];

        for (const entry of multiSessionLog) {
          const trainedOn = entry.TrainedOn || {};

          const type = trainedOn.type || 'Unknown';
          const streamingSource = trainedOn.streamingSource || null;
          const duration = entry.duration || 0;

          const startTime = entry.startTime?.toDate();
          const endTime = entry.endTime?.toDate();

          const speed = trainedOn.speed || 0;
          const pendlumLength = trainedOn.pendlumLength || 0;
          const offset = trainedOn.offset || 0;
          const oscillationTime = trainedOn.oscillationTime || 0;

          if (!startTime || isNaN(startTime.getTime())) {
            console.log('Invalid startTime, skipping entry.');
            continue;
          }

          // Prepare session data for the API request
          const sessionData = {
            sessionNumber,
            sessionDuration: duration,
            startTime: startTime.toISOString(),
            type,
            streamingSource,
            speed,
            pendlumLength,
            offset,
            oscillationTime,
            isMigration: true,
          };

          console.log('Sending session data:', sessionData);

          try {
            // Send individual API request for each MultiSession entry
            const response = await axios.put(
              `http://localhost:3002/user/${userId}/training_session`,
              sessionData,
              {
                headers: {
                  'Content-Type': 'application/json',
                },
              },
            );

            console.log('API response:', response.data);
          } catch (apiError) {
            console.error(
              'Error updating training session data via API:',
              apiError.message,
            );
          }
        }
      }
    }

    console.log('Migration completed.');
  } catch (error) {
    console.error('Migration failed:', error);
  }
};

migrateData();
