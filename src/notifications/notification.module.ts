import { Module } from '@nestjs/common';
import { NotificationService } from './notification.service';
import { FirebaseModule } from 'src/firebase/firebase.module'; // Adjust the import path as needed
import { NotificationController } from './notification.controller';

@Module({
  imports: [FirebaseModule],
  controllers: [NotificationController],
  providers: [NotificationService,],
  exports: [NotificationService],
})
export class NotificationModule {}