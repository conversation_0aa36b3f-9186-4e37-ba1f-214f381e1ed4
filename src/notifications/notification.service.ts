import {
  Injectable,
  Logger,
  HttpStatus,
  HttpException,
  NotFoundException,
} from '@nestjs/common';
import { DatabaseService } from 'src/database/database.service';
import { FirebaseService } from 'src/firebase/firebase.service';
import { UpdateNotificationsDto } from './dto/update-notifications-info.dto';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(
    private databaseService: DatabaseService,
    private firebaseService: FirebaseService,
  ) {}

  async scanTestNotifications() {
    const moment = require('moment');
    const methodName = 'scanTestNotification';
    this.logger.log(
      `NotificationsAPI - ${methodName}: Starting daily notification checker, scheduled for 6pm Swedish local time.`,
    );
    const scheduledNotifications =
      await this.databaseService.scheduled_notifications.findMany({
        where: {
          scheduled_datetime: new Date().toLocaleDateString(),
          status: 'Processing',
        },
      });

    this.logger.log(
      `Number of queries found: ${scheduledNotifications.length}`,
    );

    if (scheduledNotifications.length > 0) {
      for (const notification of scheduledNotifications) {
        const primaryID = notification.id;
        const userID = notification.user_id;

        const user = await this.databaseService.user.findUnique({
          where: { id: userID },
        });

        const userAddInfo =
          await this.databaseService.user_additional_info.findUnique({
            where: { user_id: userID },
          });

        let notificationHour = userAddInfo.notification_hour;
        if (notificationHour == null) {
          notificationHour = 9;
        }
        let localTime = moment().utc();
        localTime.local();

        if (user && notificationHour <= localTime.hours()) {
          const userUUID = user.uuid;
          this.logger.log(`uuid - ${userUUID}`);

          this.logger.log(
            `NotificationsAPI - ${methodName}: Attempting to get userDetails.`,
          );
          const firestore = this.firebaseService.getFirestore();
          const userRef = firestore.collection('UserData').doc(userUUID);
          const userDoc = await userRef.get();

          if (!userDoc.exists && !userDoc.empty) {
            this.logger.log('User Not Found!');
          } else {
            const userData = userDoc.data();
            const tokenValue = userData.token.value;
            const notificationStatus =
              userData.FeatureFlag.messages.phone_notification;
            if (tokenValue.includes('logout')) {
              this.logger.log(
                `User token value is ${tokenValue}, updating table status`,
              );
              const updateStatus =
                await this.databaseService.scheduled_notifications.update({
                  where: {
                    id: primaryID,
                  },
                  data: {
                    status: 'User Logged Out',
                  },
                });
              this.logger.log(`Status update of table: ${updateStatus}`);
              return updateStatus;
            } else if (tokenValue && notificationStatus) {
              // Token value should not be logout and notification hour should be correct.
              // Send push notification
              const message = {
                token: tokenValue,
                notification: {
                  title: notification.title,
                  body: notification.body,
                },
                android: {
                  priority: 'high', // Ensure high priority for timely delivery
                },
                apns: {
                  payload: {
                    aps: {
                      alert: {
                        title: notification.title,
                        body: notification.body,
                      },
                      sound: 'default',
                      badge: 1,
                    },
                  },
                },
              };
              const messaging = this.firebaseService.getMessaging();
              try {
                if (!messaging) {
                  throw new Error(
                    'Messaing service is undefined. Ensure Firebase is initialized correctly!',
                  );
                }
                const response = await messaging.send(message);
                this.logger.log(`Response of message send: ${response.data}`);
                const updateStatus =
                  await this.databaseService.scheduled_notifications.update({
                    where: {
                      id: primaryID,
                    },
                    data: {
                      status: 'Sent',
                    },
                  });
                this.logger.log(`Status update of table: ${updateStatus}`);
                return updateStatus;
              } catch (error) {
                this.logger.error(`Error sending message: ${error.message}`);
              }
            } else {
              this.logger.error(
                `NotificationAPI - ${methodName}: Token value for user not valid: ${tokenValue} or user does not have notifications enabled: ${notificationStatus}`,
              );
            }
          }
        } else {
          this.logger.error(
            `NotificationAPI - ${methodName}: User not found - User ID: ${userID}, or hour not yet: ${notificationHour}`,
          );
          throw new NotFoundException(
            'User not found or notification time wrong.',
          );
        }
      }
    } else {
      this.logger.log(
        `NotificationsAPI - ${methodName}: No users found with scheduled notifications`,
      );
      throw new HttpException(
        'Found no users with scheduled notifications for today.',
        HttpStatus.NOT_FOUND,
      );
    }
  }

  async updateNotificationsInfo(userUUID: string, dto: UpdateNotificationsDto) {
    const methodName = 'updateNotificationsInfo';
    let userID = null;

    if (!userUUID) {
      this.logger.log(
        `NotificationsAPI - ${methodName}: User not found - UserId: ${userUUID}`,
      );
      throw new NotFoundException('User Not Found!');
    }
    try {
      if (dto.notificationsEnabled !== undefined) {
        const firestore = this.firebaseService.getFirestore();
        const userDoc = firestore.collection('UserData').doc(userUUID);
        await userDoc.set(
          {
            FeatureFlag: {
              messages: {
                phone_notification: dto.notificationsEnabled,
              },
            },
          },
          { merge: true },
        );
      }

      if (dto.notificationHour !== undefined) {
        const user = await this.databaseService.user.findUnique({
          where: { uuid: userUUID },
        });
        userID = user.id;

        const updateUserAddInfo =
          await this.databaseService.user_additional_info.update({
            where: { user_id: userID },
            data: { notification_hour: dto.notificationHour || undefined },
          });
      }
    } catch (error) {
      this.logger.error(
        `${methodName} - Failed to send verification email or update database for UID: ${userID}: ${error.message}`,
        error.stack,
      );
    }
  }

  async fetchDailyReminders() {
    const enStrings = {
      hi: 'Hi {0}',
      trainToday: "It's time for you to train!",
      trainTodayGoals:
        "It's time for you to train! You have {0} days left to reach your goal this week.",
      miss2Days:
        'You have missed two days of training in a row. This is not good. It is important that you train at least 4 times a week',
      miss3Days:
        '3 days have now passed without you having trained. This is not good at all. It is important that you train at least 4 times a week and do not have two full days of rest to achieve the best results.',
      miss4Days:
        'You have missed 4 days. It is important that you train at least 4 times a week to achieve the best results. Right now it looks like you will miss your training goal. If you do not continue to train now, you will soon have to prolong your training to get lasting results.',
      miss5Days:
        'You have missed 5 days of training. It is important that you train at least 4 times a week to achieve the best results. Do you want to risk missing your training goals and not get the results you want? You need to start training now so you do not need to prolong your training!',
      miss6Days:
        '{0} days without training. You need to get started today so that you do not need to prolong your training. If you do not get started today, we will contact you tomorrow.',
    };

    const svStrings = {
      hi: 'Hej {0}',
      trainToday: 'Det är dags för dig att träna!',
      trainTodayGoals:
        'Det är dags för dig att träna! Du har {0} dagar kvar för att nå ditt mål denna veckan.',
      miss2Days:
        'Du har missat två dagars träning i rad. Detta är inte bra. Det är viktigt att du tränar minst 4 gånger i veckan',
      miss3Days:
        '3 dagar har nu gått utan att du har tränat. Detta är inte alls bra. Det är viktigt att du tränar minst 4 gånger i veckan och inte har två hela dagar med vila för att nå bästa resultat.',
      miss4Days:
        'Du har missat 4 dagar. Det är viktigt att du tränar minst 4 gånger i veckan för att nå bästa resultat. Just nu ser det ut som om du kommer att missa ditt träningsmål. Om du inte fortsätter träna nu så måste du snart förlänga din träning för att få bestående resultat.',
      miss5Days:
        'Du har missat 5 dagars träning. Det är viktigt att du tränar minst 4 gånger i veckan för att nå bästa resultat. Vill du riskera att missa dina träningsmål och inte få de resultat du önskar? Du måste börja träna nu för att inte behöva förlänga din träning!',
      miss6Days:
        '{0} dagar utan träning. Du måste komma igång idag för att inte behöva förlänga din träning. Kommer du inte igång idag, kommer vi att kontakta dig imorgon.',
    };

    const moment = require('moment');
    const currentTime = moment().startOf('day');
    let skippedUsers = 0;
    const firestore = this.firebaseService.getFirestore();
    this.logger.log(`Current Time Daily Reminders: ${currentTime}`);

    try {
      const docRefs = firestore.collection('UserData');
      const query = docRefs.where('ValidTill', '>', currentTime);
      const docSnapshots = await query.get();
      this.logger.log(`Number of user documents: ${docSnapshots.size}`);

      for (const element of docSnapshots.docs) {
        if (!element.exists) {
          this.logger.log('Error, Doc Snapshot not found: ', element.id);
          continue;
        }

        const docData = element.data();
        //this.logger.log('Document Data: ', docData);
        if (
          docData &&
          docData.LastSessionDoneAt &&
          docData.LastSessionDoneAt._seconds &&
          docData.Programs
        ) {
          const diffDays = currentTime.diff(
            moment.unix(docData.LastSessionDoneAt._seconds).startOf('day'),
            'day',
          );
          const validDiff = currentTime.diff(
            moment.unix(docData.ValidTill._seconds).startOf('day'),
            'day',
          );
          const lastSessionWeekEnd = moment
            .unix(docData.LastSessionDoneAt._seconds)
            .endOf('isoWeek');
          let language =
            (docData.DeviceInfo && docData.DeviceInfo.languageCode) || 'en';
          if (!['en', 'sv'].includes(language)) {
            language = 'en';
          }
          const programsSnap = await firestore
            .doc(docData.Programs._path.segments.join('/'))
            .get();

          if (!programsSnap.exists) {
            this.logger.log('Program Snapshot Does Not Exist');
            continue;
          }
          const programData = programsSnap.data().Program[0];
          const currentWeekProgress = docData.CurrentWeekProgress;
          const isSubscribed = !!programData.ProgramSubscribed;
          const sessionsDone = programData.SessionsDone > 0 || false;
          let completedDays =
            (currentWeekProgress && currentWeekProgress.completedDays) || 0;
          if (
            currentWeekProgress &&
            currentWeekProgress.updatedOn &&
            currentWeekProgress.updatedOn._seconds &&
            moment
              .unix(currentWeekProgress.updatedOn._seconds)
              .endOf('day')
              .isAfter(lastSessionWeekEnd)
          ) {
            this.logger.log('Completed days are 0');
            completedDays = 0;
          }
          const targetDays =
            (currentWeekProgress && currentWeekProgress.targetDays) ||
            docData.WeekelyTrainingTarget ||
            4;

          if (
            diffDays > 0 &&
            completedDays !== targetDays &&
            validDiff <= 0 &&
            sessionsDone &&
            isSubscribed
          ) {
            this.logger.log('Difference in days trained found!');
            const user = await this.databaseService.user.findUnique({
              where: { uuid: element.id },
            });
            const languageStrings = language === 'sv' ? svStrings : enStrings;
            let name = `${docData.FirstName || ''} ${
              docData.LastName || ''
            }`.trim();
            let title = languageStrings.hi.replace('{0}', name);
            let notificationBody = '';

            if (diffDays <= 1) {
              if (completedDays === targetDays) {
                continue;
              }
              notificationBody =
                completedDays > 0
                  ? languageStrings.trainTodayGoals.replace(
                      '{0}',
                      (targetDays - completedDays).toString(),
                    )
                  : languageStrings.trainToday;
            } else {
              const dayDifferenceMessage = {
                2: languageStrings.miss2Days,
                3: languageStrings.miss3Days,
                4: languageStrings.miss4Days,
                5: languageStrings.miss5Days,
              };

              notificationBody =
                dayDifferenceMessage[diffDays] ||
                languageStrings.miss6Days.replace('{0}', diffDays.toString());
            }
            this.logger.log(`Adding notification for user: ${element.id}`);
            await this.databaseService.scheduled_notifications.create({
              data: {
                user_id: user.id,
                title: title,
                body: notificationBody,
                scheduled_datetime: new Date().toLocaleDateString(),
                status: 'Processing',
              },
            });

            this.logger.log(
              `Fetched Daily Reminder and created notification for user: ${element.id}`,
            );
          } else {
            skippedUsers++;
          }
        } else {
          skippedUsers++;
        }
      }
      this.logger.log(`Skipped Users for notifications: ${skippedUsers}`);
    } catch (error) {
      this.logger.error('Error in fetchDailyReminders:', error);
      throw new HttpException(
        'Error creating daily reminders for users!',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
