import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsBoolean, IsInt, IsOptional } from 'class-validator'

export class UpdateNotificationsDto {
    @ApiProperty({
        required: false,
        description: 'Whether the notifications are enabled or not'
    })
    @IsBoolean()
    notificationsEnabled: boolean;

    @ApiProperty({
        required: false,
        description: 'The hour of notification',
    })
    @IsOptional()
    @IsInt()
    notificationHour?: number;

    
}