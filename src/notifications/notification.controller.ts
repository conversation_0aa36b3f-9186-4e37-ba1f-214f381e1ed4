import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Res,
} from '@nestjs/common';
import {
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Response } from 'express';
import { NotificationService } from './notification.service';
import { APIKeyConfiguration } from 'node-mailjet';
import { UpdateNotificationsDto } from './dto/update-notifications-info.dto';
@ApiTags('notifications')
@Controller('notifications')
export class NotificationController {
  constructor(private notificationService: NotificationService) {}

  @Get('/scan-tests-notification')
  @ApiOperation({
    summary: 'Scan scheduled tests for notifications',
    description:
      'Scans for all scheduled notifications due todays date and sends out a notification to the respective users device about the test',
  })
  async getTests(@Res() res: Response) {
    try {
      const result = await this.notificationService.scanTestNotifications();
      res.json(result);
    } catch (error) {
      res
        .status(HttpStatus.NOT_FOUND)
        .json({ error: error.message });
    }
  }

  @Post('/:userUUID/update-notifications-info')
  @ApiOperation({
    summary: 'Update notification settings',
    description:
      'Update the notification settings and notification hour for user',
  })
  @ApiParam({
    name: 'userUUID',
    description:
      'The UUID of the user whose notification settings are being updated',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Notification info updated successfully',
  })
  async updateNotifications(
    @Param('userUUID') userUUID: string,
    @Body() updateNotificationsDto: UpdateNotificationsDto,
    @Res() res: Response,
  ) {
    try {
      const result = await this.notificationService.updateNotificationsInfo(
        userUUID,
        updateNotificationsDto,
      );
      res.status(HttpStatus.OK).json(result);
    } catch (error) {
      res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ error: error.message });
    }
  }

  @Get('/fetch-daily-reminders')
  @ApiOperation({
    summary: 'Get list of daily reminders',
    description:
      'Fetch the daily reminders for users who have not trained.',
  })
  async fetchReminders(@Res() res: Response) {
    try {
      const result = await this.notificationService.fetchDailyReminders();
      res.json(result);
    } catch (error) {
      res
      .status(HttpStatus.NOT_FOUND)
      .json({ error: error.message });
    }
  }
}
