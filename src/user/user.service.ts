import {
  Body,
  Injectable,
  Inject,
  Res,
  Logger,
  HttpException,
  HttpStatus,
  NotFoundException,
} from '@nestjs/common';
import { DatabaseService } from 'src/database/database.service';
import { FirebaseService } from 'src/firebase/firebase.service';
import { MailService } from '../mail/mail.service';
import { ResetPasswordDto } from './dto/reset-password';
import { LinkGuardianAccountDto } from './dto/link-guardian-account';

import { WINSTON_MODULE_NEST_PROVIDER } from 'nest-winston';
import { LinkUserToStudentDto } from './dto/link-user-to-student.dto';
import { UpdateDeviceDataDto } from './dto/update-device-data.dto';
import * as CryptoJS from 'crypto-js';
import { TrainingConfigDto } from './dto/training-config.dto';
import { RecoverUsernameDto } from './dto/recover-username-dto';
import { CreateUserDto } from './dto/create-user-no-uuid';

interface User {
  user_id: string;
  registered_on: Date;
  email: string;
  first_name: string;
  last_name: string;
  latest_average: number | string;
  previous_three_average: number | string;
}

interface PaginatedUsers {
  users: User[];
  total: number;
}
@Injectable({})
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    private database: DatabaseService,
    private firebaseService: FirebaseService,
    private mailService: MailService,
  ) {}

  async linkGuardianAccount(
    linkGuardianAccountDto: LinkGuardianAccountDto,
  ): Promise<{ guardianAccountLinked: boolean }> {
    const apiName = '[linkGuardianAccount API]';
    try {
      this.logger.log(`${apiName} Starting guardian account linking process.`);
      const childAccountUUID = linkGuardianAccountDto.childAccountUUID;
      const guardianInfo = linkGuardianAccountDto.guardianInfo;

      /**
       * check if this info was used previously to verify a child acount
       * if not, create a new parent info record
       */
      this.logger.log(
        `${apiName} Upserting parent info for email: ${guardianInfo.email}`,
      );
      const parentInfo = await this.database.parent_info.upsert({
        where: { email: guardianInfo.email },
        update: {
          first_name: guardianInfo.first_name,
          last_name: guardianInfo.last_name,
          email: guardianInfo.email,
        },
        create: {
          first_name: guardianInfo.first_name,
          last_name: guardianInfo.last_name,
          email: guardianInfo.email,
        },
      });

      /**
       * link child and guardian accounts in database
       */
      // lookup child account
      const childAccount = await this.database.user.findUnique({
        where: { uuid: childAccountUUID },
      });

      if (childAccount) {
        this.logger.log(
          `${apiName} Child account found. UUID: ${childAccountUUID}`,
        );
        // lookup guardian account
        const guardianAccount = await this.database.user.findFirst({
          where: {
            type: 'Guardian',
            additional_info: { email: guardianInfo.email },
          },
          include: {
            additional_info: true,
          },
        });

        // if guardian account exists we link the accounts
        if (guardianAccount) {
          this.logger.log(
            `${apiName} Linking child and guardian accounts in database.`,
          );
          const _ = await this.database.user_guardian.create({
            data: {
              user_id: childAccount.id,
              guardian_id: guardianAccount.id,
            },
          });
        }

        // we attach the parent info to child account for later reference
        this.logger.log(`${apiName} Attaching parent info to child account.`);
        const _ = await this.database.user.update({
          where: { id: childAccount.id },
          data: { parent_info_id: parentInfo.id },
        });
      } else {
        this.logger.warn(
          `${apiName} No child account found for UUID: ${childAccountUUID}`,
        );
      }

      /**
       * link accounts in firebase
       * TODO : to be removed after we do the full transition to the db
       */

      const firestore = this.firebaseService.getFirestore();

      // lookup firebase child account
      const firebaseChildAccountRef = firestore
        .collection('UserData')
        .doc(childAccountUUID);
      const firebaseChildAccount = await firebaseChildAccountRef.get();

      if (firebaseChildAccount.exists) {
        this.logger.log(
          `${apiName} Firebase child account found. Proceeding to link with guardian account.`,
        );
        const firebaseGuardianAccountRef = firestore
          .collection('UserData')
          .where('Email', '==', guardianInfo.email);
        const firebaseGuardianAccountSnapshot =
          await firebaseGuardianAccountRef.get();

        // if firebase guardian account exists we link the two accounts
        if (!firebaseGuardianAccountSnapshot.empty) {
          const guardianAccountDoc = firebaseGuardianAccountSnapshot.docs[0];
          const guardianAccountData = guardianAccountDoc.data();

          const monitoringUsers = guardianAccountData.MonitoringUsers || [];
          monitoringUsers.push({
            UserApprovedMonitoring: true,
            UserId: firestore.doc(`UserData/${firebaseChildAccount.id}`),
          });

          const selectedFirebaseGuardianAccountRef = firestore
            .collection('UserData')
            .doc(guardianAccountDoc.id);

          await selectedFirebaseGuardianAccountRef.update({
            MonitoringUsers: monitoringUsers,
          });

          this.logger.log(`${apiName} Firebase accounts linked successfully.`);
        } else {
          // otherwise we attach the guardian email to child account for later reference
          await firebaseChildAccountRef.update({
            GuardianEmail: guardianInfo.email,
          });
          this.logger.log(
            `${apiName} Guardian email attached to Firebase child account.`,
          );
        }
      } else {
        this.logger.warn(
          `${apiName} No Firebase child account found for UUID: ${childAccountUUID}`,
        );
      }
      return { guardianAccountLinked: true };
    } catch (error) {
      this.logger.error(
        `${apiName} Error linking guardian account: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async checkUserSubscription(
    uuid: string,
  ): Promise<{ hasSubscription: boolean }> {
    const apiName = '[checkUserSubscription API]';
    try {
      this.logger.log(
        `${apiName} Checking subscription status for UUID: ${uuid}`,
      );
      const user = await this.database.user.findUnique({
        where: { uuid },
      });

      if (!user) {
        this.logger.error(`${apiName} User not found with UUID: ${uuid}`);
        return { hasSubscription: false };
      }

      // Retrieve all purchase activations for this user
      const purchaseActivations = await this.database.activation.findMany({
        where: { user_id: user.id },
      });
      // If no purchase activations are found, return hasSubscription as false
      if (!purchaseActivations.length) {
        this.logger.error(
          `${apiName} No purchase activations found for user with UUID: ${uuid}`,
        );
        return { hasSubscription: false };
      }

      this.logger.log(
        `${apiName} Found purchase activations for UUID: ${uuid}. Checking for subscriptions.`,
      );
      // Check each purchase activation to see if any corresponding purchase is a subscription
      for (const activation of purchaseActivations) {
        const purchase = await this.database.purchase.findUnique({
          where: { id: activation.purchase_id },
        });

        // If a subscription is found, return hasSubscription as true
        if (purchase && purchase.is_subscription) {
          this.logger.log(`${apiName} Subscription found for UUID: ${uuid}.`);
          return { hasSubscription: true };
        }
      }

      // If no subscriptions are found, return hasSubscription as false
      this.logger.log(`${apiName} No subscriptions found for UUID: ${uuid}.`);
      return { hasSubscription: false };
    } catch (error) {
      this.logger.error(
        `${apiName} Error checking user subscription for UUID: ${uuid}: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async checkIsStudent(userUUID: string): Promise<{ isStudent: boolean }> {
    const apiName = '[checkIsStudent API]';
    try {
      this.logger.log(`${apiName} Checking if user is a student.`);
      const user = await this.database.user.findUnique({
        where: { uuid: userUUID },
      });

      if (!user) {
        this.logger.error(`${apiName} User not found with UUID: ${userUUID}`);
        return { isStudent: false };
      }

      const isStudent = !!(await this.database.student.findUnique({
        // Creates a true boolean value from the student object if the entry exists, else false
        where: { userId: user.id },
      }));

      return { isStudent };
    } catch (error) {
      this.logger.error(
        `${apiName} Error checking if user is a student: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async submitSurvey(
    userId: number,
    questionIndex: number,
    answerArray: number[],
  ): Promise<{ success: boolean }> {
    const apiName = '[submitSurvey API]';
    try {
      this.logger.log(
        `${apiName} Submitting survey responses for user ID: ${userId} and question index: ${questionIndex}`,
      );

      // Prepare the data for insertion
      const surveyData = answerArray.map((answerIndex) => ({
        user_id: userId,
        question_index: questionIndex,
        answer_index: answerIndex,
      }));

      // Insert the data
      await this.database.userSurvey.createMany({
        data: surveyData,
      });

      this.logger.log(`${apiName} Survey responses successfully recorded.`);
      return { success: true };
    } catch (error) {
      this.logger.error(
        `${apiName} Error submitting survey for user ID: ${userId}: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async hasUserAnsweredSurvey(
    userUUID: string,
  ): Promise<{ hasAnswered: number }> {
    const apiName = '[hasUserAnsweredSurvey API]';
    try {
      this.logger.log(`${apiName} Checking if user has answered survey.`);
      enum SurveyStatus {
        NOT_ANSWERED = 0,
        ANSWERED_ONLY_FIRST_SURVEY = 1,
        ANSWERED_ALL_SURVEY = 2,
        USER_NOT_FOUND = 3,
      }
      const user = await this.database.user.findUnique({
        where: { uuid: userUUID },
      });
      if (!user) {
        this.logger.error(`${apiName} User not found with UUID: ${userUUID}`);
        return { hasAnswered: SurveyStatus.USER_NOT_FOUND }; // Returning 3 if user not found
      } else {
        const numberOfSurveysAnswered = await this.database.userSurvey.findMany(
          {
            where: { user_id: user.id },
          },
        );
        this.logger.log('Number of surveys answered:', numberOfSurveysAnswered);
        if (numberOfSurveysAnswered.length < 1) {
          this.logger.log(`${apiName} User has not answered survey.`);
          return { hasAnswered: SurveyStatus.NOT_ANSWERED }; // Returning 0 if user has not answered any survey
        } else if (
          numberOfSurveysAnswered.some((survey) => survey.question_index === 2)
        ) {
          this.logger.log(`${apiName} User has answered all survey questions.`);
          return { hasAnswered: SurveyStatus.ANSWERED_ALL_SURVEY }; // Returning 2 if user has answered both surveys
        } else {
          this.logger.log(`${apiName} User has answered survey 1 but not 2.`);
          return {
            hasAnswered: SurveyStatus.ANSWERED_ONLY_FIRST_SURVEY,
          }; // Returning 1 if user has answered only survey 1 and not 2
        }
      }
    } catch (error) {
      this.logger.error(
        `${apiName} Error checking if user has answered survey: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async resetChildAccountPassword(
    data: ResetPasswordDto,
  ): Promise<{ success: boolean }> {
    const apiName = '[resetChildAccountPassword API]'; // API name for logging
    try {
      this.logger.log(
        `${apiName} Initiating password reset for child account email: ${data.childAccountEmail}`,
      );

      const auth = this.firebaseService.getAuth();
      const firestore = this.firebaseService.getFirestore();

      // get user info
      const userInfoSnapshot = await firestore
        .collection('UserData')
        .where('Email', '==', data.childAccountEmail)
        .get();

      if (!userInfoSnapshot.empty) {
        const userData = userInfoSnapshot.docs[0].data();
        const userEmail = userData.Email;
        const userGuardianEmail = userData.GuardianEmail;

        // send reset password email to the guardian
        if (userEmail && userGuardianEmail) {
          const resetPasswordLink =
            await auth.generatePasswordResetLink(userEmail);

          await this.mailService.sendMail(
            userGuardianEmail,
            '',
            'IMVI Reset Password',
            `Please use the following url to reset the password:\n ${resetPasswordLink}`,
            `<h3>Please use the following url to reset the password:</h3><br />${resetPasswordLink}`,
          );
          this.logger.log(
            `${apiName} Password reset email sent to guardian email: ${userGuardianEmail}`,
          );
        } else {
          this.logger.warn(
            `${apiName} Email or guardian email not found for user.`,
          );
        }
      } else {
        this.logger.warn(
          `${apiName} No user found with email: ${data.childAccountEmail}`,
        );
      }

      return { success: true };
    } catch (error) {
      this.logger.error(
        `${apiName} Error resetting child account password: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async sendResetPasswordToAdmin(
    data: ResetPasswordDto,
  ): Promise<{ success: boolean }> {
    const apiName = '[sendResetPasswordToAdmin API]'; // API name for logging
    try {
      this.logger.log(
        `${apiName} Initiating password reset for account email: ${data.childAccountEmail}`,
      );

      const adminEmail = '<EMAIL>';

      const auth = this.firebaseService.getAuth();

      const resetPasswordLink = await auth.generatePasswordResetLink(
        data.childAccountEmail,
      );

      await this.mailService.sendMail(
        adminEmail,
        '',
        'IMVI Reset Password',
        `Please use the following url to reset the password:\n ${resetPasswordLink}`,
        `<h3>Please use the following url to reset the password:</h3><br />${resetPasswordLink}`,
      );
      this.logger.log(
        `${apiName} Password reset email sent to admin email: ${adminEmail}`,
      );

      return { success: true };
    } catch (error) {
      this.logger.error(
        `${apiName} Error resetting password: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async disableFirebaseUser(
    uuid: string,
  ): Promise<{ success: boolean; message?: string }> {
    try {
      await this.firebaseService.disableUser(uuid);
      return {
        success: true,
        message: `User with UUID ${uuid} has been disabled.`,
      };
    } catch (error) {
      this.logger.error(`Error disabling user with UUID: ${uuid}`, error.stack);
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getParentInfoByUserUuid(
    uuid: string,
  ): Promise<{ email: string; first_name: string; last_name: string } | null> {
    try {
      const userWithParentInfo = await this.database.user.findUnique({
        where: { uuid },
        include: { parent_info: true },
      });

      if (!userWithParentInfo) {
        this.logger.error(`User not found with UUID: ${uuid}`);
        throw new Error('User not found');
      }

      if (!userWithParentInfo.parent_info) {
        this.logger.error(`Parent info not found for user with UUID: ${uuid}`);
        throw new Error('Parent info not found for this user');
      }

      this.logger.log(`Parent info retrieved for user with UUID: ${uuid}`);
      return {
        email: userWithParentInfo.parent_info.email,
        first_name: userWithParentInfo.parent_info.first_name,
        last_name: userWithParentInfo.parent_info.last_name,
      };
    } catch (error) {
      this.logger.error(
        `Error retrieving parent info for UUID: ${uuid}`,
        error.stack,
      );
      throw new HttpException(error.message, HttpStatus.NOT_FOUND);
    }
  }

  async linkUserToStudent(
    linkUserToStudentDto: LinkUserToStudentDto,
  ): Promise<void> {
    const { user_id, student_id } = linkUserToStudentDto;

    // Check if the user exists
    const user = await this.database.user.findUnique({
      where: { id: user_id },
    });
    if (!user) {
      throw new NotFoundException(`User with ID ${user_id} not found`);
    }

    // Check if the student exists
    const student = await this.database.student.findUnique({
      where: { id: student_id },
    });
    if (!student) {
      throw new NotFoundException(`Student with ID ${student_id} not found`);
    }

    // Link the user to the student
    await this.database.student.update({
      where: { id: student_id },
      data: { userId: user_id },
    });
  }

  async getAdditionalInfoByUserUUID(uuid: string): Promise<{
    id: number;
    name: string;
    email: string;
    age: number;
    vision_problem: string;
    optional_text: string;
    accept_newsletter: boolean;
    user_id: number;
    first_name: string;
    birthdate: Date;
    notification_hour: number;
    change_flag: boolean;
    read_calibration_from_backend: boolean;
    performed_test_today: boolean;
    has_double_vision: boolean;
  } | null> {
    try {
      const user = await this.database.user.findUnique({
        where: { uuid: uuid },
      });
      if (!user) {
        this.logger.error(`User not found with UUID: ${uuid}`);
        throw new Error('User not found');
      }
      const userID = user.id;

      const userAddInfo = await this.database.user_additional_info.findUnique({
        where: { user_id: userID },
      });
      if (!userAddInfo) {
        this.logger.log('User Additional Info not found.');
        throw new Error('Additional Info not found');
      }
      const notificationHour = userAddInfo.notification_hour;

      if (notificationHour === null) {
        userAddInfo.notification_hour = 9;
        return userAddInfo;
      } else {
        return userAddInfo;
      }
    } catch (error) {
      this.logger.error(
        `Error retrieving additional info for UUID: ${uuid}`,
        error.stack,
      );
      throw new HttpException(error.message, HttpStatus.NOT_FOUND);
    }
  }

  async getAllUsers(
    page: number = 1,
    limit: number = 50,
    search?: string,
    sortBy: string = 'registered_on',
    sortOrder: string = 'asc',
    filterType: string = 'all',
  ): Promise<PaginatedUsers> {
    try {
      const sortFieldMap = {
        latest_average: 'latest_average',
        previous_three_average: 'previous_three_average',
        email: 'email',
        first_name: 'first_name',
        last_name: 'last_name',
        registered_on: 'registered_on',
        standard_deviation: 'standard_deviation',
        calibration_standard_deviation: 'calibration_standard_deviation',
        first_training_date: 'first_training_date',
        last_training_date: 'last_training_date',
        next_training_date: 'next_training_date',
        vergence_test_count: 'vergence_test_count',
        valid_until: 'valid_until',
        change_flag: 'change_flag',
      };

      // Validate and map the sort field
      const sortField = sortFieldMap[sortBy] || 'registered_on';

      // Validate sort order
      const sortDirection = sortOrder.toUpperCase() === 'DESC' ? 'DESC' : 'ASC';
      const offset = (page - 1) * limit;

      const result = await this.database.query(
        'CALL imviread.GetUserCalibrationsAveragesWithSortAndSearch(?, ?, ?, ?, ?, ?)',
        [sortField, sortDirection, search, limit, offset, filterType],
      );

      // Ensure result is an array of rows
      if (!Array.isArray(result) || result.length === 0) {
        return { users: [], total: 0 };
      }

      // Extract and map the rows to meaningful keys
      const usersData = result.map((user: any) => {
        const mappedUser = {
          user_id: user.user_id ? user.user_id.toString() : user.f0?.toString(),
          user_uuid: user.user_uuid || user.f1,
          registered_on: user.registered_on || user.f2,
          email: user.email || user.f3 || '-',
          first_name: user.first_name || user.f4 || '-',
          last_name: user.last_name || user.f5 || '-',
          latest_average: user.latest_average || user.f6 || '-',
          previous_three_average: user.previous_three_average || user.f7 || 0,
          standard_deviation: user.standard_deviation || user.f8 || 0,
          calibration_standard_deviation:
            user.calibration_standard_deviation || user.f9 || 0,
          first_training_date: user.first_training_date || user.f10 || '-',
          last_training_date: user.last_training_date || user.f11 || '-',
          next_training_date: user.next_training_date || user.f12 || '-',
          vergence_test_count: user.f13 ? user.f13.toString() : '0',
          valid_until: user.f14 ? user.f14 : null,
          change_flag: user.f15 ? Boolean(user.f15) : false,
          read_from_backend: user.f16 ? Boolean(user.f16) : false,
          performed_test_today: Boolean(user.f17),
          has_double_vision: Boolean(user.f18),
          type: user.f21,
          starred: Boolean(user.f22),
        };

        return mappedUser;
      });

      const total = result[0]?.f19 ? Number(result[0].f19) : 0;

      return {
        users: usersData,
        total,
      };
    } catch (error) {
      console.error('Error in getAllUsers:', error.message);
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Utility function to sanitize the search parameter
  sanitizeSearch(search: string): string {
    if (!search) return search;
    // keep any kind of letter (including å, é, ü, etc.), digits, spaces, hyphens, underscores, @, dot
    const sanitized = search.replace(/[^\p{L}0-9\s\-_.@]/gu, '');
    return sanitized.trim();
  }

  async saveTrainingSessionData(data: {
    sessionNumber: number;
    sessionDuration: number;
    startTime: Date;
    userId: string;
    type: string;
    streamingSource?: string;
    speed: number;
    pendlumLength: number;
    offset: number;
    oscillationTime: number;
    isMigration: boolean; // If true, SKIP updating valid_until
    deviceId?: number; // New field to link the training session to a device
  }): Promise<string> {
    this.logger.log('Starting training data insertion');

    const {
      sessionNumber,
      sessionDuration,
      startTime,
      userId,
      type,
      streamingSource,
      speed,
      pendlumLength,
      offset,
      oscillationTime,
      isMigration,
      deviceId,
    } = data;

    try {
      // Find the user based on UUID
      const user = await this.database.user.findUnique({
        where: { uuid: userId },
      });

      if (!user) {
        this.logger.error(`User with UUID ${userId} not found`);
        throw new Error(`User with UUID ${userId} not found`);
      }

      // Check if this is the first test for the user
      const existingSessions =
        await this.database.training_session_data.findFirst({
          where: { user_id: user.id },
        });

      // Insert new training session data
      await this.database.training_session_data.create({
        data: {
          user_id: user.id,
          session_number: sessionNumber,
          session_duration: sessionDuration,
          start_time: startTime,
          type,
          streamingSource: streamingSource || null,
          speed,
          pendlumLength,
          offset,
          oscillationTime,
          deviceId: deviceId,
        },
      });

      // If it's the first test AND isMigration is false, update valid_until
      if (!existingSessions && !isMigration) {
        this.logger.log(
          'This is the first test for the user. Updating valid_until...',
        );

        const purchaseActivation = await this.database.activation.findFirst({
          where: { user_id: user.id },
          select: { purchase_id: true },
        });

        if (purchaseActivation?.purchase_id) {
          const purchase = await this.database.purchase.findUnique({
            where: { id: purchaseActivation.purchase_id },
            select: { duration: true },
          });

          if (purchase?.duration) {
            const validUntilDate = new Date();
            validUntilDate.setDate(
              validUntilDate.getDate() + purchase.duration,
            );
            console.log(validUntilDate);
            // Update valid_until in SQL
            await this.database.user.update({
              where: { id: user.id },
              data: { valid_until: validUntilDate },
            });

            this.logger.log(
              `Updated valid_until for user ${userId} to ${validUntilDate}`,
            );

            // Update Firestore ValidTill
            const firestore = this.firebaseService.getFirestore();
            const firebaseUserRef = firestore
              .collection('UserData')
              .doc(userId);
            const firebaseUserDoc = await firebaseUserRef.get();

            if (firebaseUserDoc.exists) {
              await firebaseUserRef.update({ ValidTill: validUntilDate });
              this.logger.log(
                `Updated ValidTill in Firebase for user ${userId}`,
              );
            } else {
              this.logger.warn(`No Firebase document found for user ${userId}`);
            }
          } else {
            this.logger.warn(
              `No duration found for purchase_id ${purchaseActivation.purchase_id}`,
            );
          }
        } else {
          this.logger.warn(
            `No purchase_id found for user ${userId} in purchase_activation`,
          );
        }
      }

      this.logger.log('Training session data inserted successfully');
      return 'Training session data saved successfully';
    } catch (error) {
      this.logger.error('Error saving training data:', error.message);
      throw new Error('Failed to save training data');
    }
  }

  async getTrainingSessionData(userId: string) {
    this.logger.log('Fetching training session data');
    try {
      const user = await this.database.user.findUnique({
        where: { uuid: userId },
      });

      if (!user) {
        throw new Error(`User with UUID ${userId} not found`);
      }

      const trainingSessions =
        await this.database.training_session_data.findMany({
          where: { user_id: user.id },
          orderBy: { start_time: 'desc' },
          select: {
            session_number: true,
            session_duration: true,
            start_time: true,
            type: true,
            streamingSource: true,
            speed: true,
            pendlumLength: true,
            offset: true,
            oscillationTime: true,
          },
        });

      return trainingSessions;
    } catch (error) {
      this.logger.error('Error fetching training session data:', error);
      throw new Error('Failed to fetch training session data');
    }
  }

  async saveCalibrationData(calibrationData: {
    array: number[];
    userId: string;
    datetime: string;
    deviceId?: number;
  }): Promise<string> {
    this.logger.log('CalibrationService - Starting calibration data insertion');

    const { array, userId, datetime, deviceId } = calibrationData;

    try {
      const user = await this.database.user.findUnique({
        where: { uuid: userId },
      });

      if (!user) {
        throw new Error(`User with UUID ${userId} not found`);
      }

      const calibrationRecord = await this.database.calibration.create({
        data: {
          user_id: user.id,
          calibration_date: new Date(datetime),
          deviceId: deviceId,
        },
      });

      const calibrationDataEntries = array.map((value) => ({
        value,
        calibration_id: calibrationRecord.id,
      }));

      await this.database.calibration_data.createMany({
        data: calibrationDataEntries,
      });

      this.logger.log(
        'CalibrationService - Calibration data inserted successfully',
      );
      return 'Calibration data saved successfully';
    } catch (error) {
      this.logger.error(
        'CalibrationService - Error saving calibration data:',
        error,
      );
      throw new Error('Failed to save calibration data');
    }
  }

  async getCalibrationDataByUserUuid(userId: string) {
    this.logger.log(`Fetching calibration data for user UUID: ${userId}`);

    // Fetch user ID based on UUID
    const user = await this.database.user.findUnique({
      where: { uuid: userId },
    });

    if (!user) {
      throw new Error(`User with UUID ${userId} not found`);
    }

    // Fetch all calibrations ordered by date descending
    const calibrationRecords = await this.database.calibration.findMany({
      where: { user_id: user.id },
      include: {
        calibration_data: true,
      },
      orderBy: {
        calibration_date: 'desc',
      },
    });

    // Filter to keep only the latest calibration for each day
    const latestCalibrationsPerDay: typeof calibrationRecords = [];
    const seenDates = new Set<string>();

    for (const record of calibrationRecords) {
      const dateKey = record.calibration_date.toISOString().split('T')[0];
      if (!seenDates.has(dateKey)) {
        latestCalibrationsPerDay.push(record);
        seenDates.add(dateKey);
      }
    }

    // For each calibration we have a deviceId; we go into the device_data table and retrieve it.
    const calibrationsWithDeviceData = await Promise.all(
      latestCalibrationsPerDay.map(async (record) => {
        if (record.deviceId) {
          const deviceData = await this.database.device_data.findUnique({
            where: { id: record.deviceId },
          });
          return { ...record, RecordedDeviceData: deviceData };
        }
        return record;
      }),
    );

    return calibrationsWithDeviceData;
  }

  async saveValidUntilDate(validUntilData: {
    userId: string;
    validUntil: Date;
  }): Promise<string> {
    const { userId, validUntil } = validUntilData;

    try {
      const user = await this.database.user.findUnique({
        where: { uuid: userId },
      });

      if (!user) {
        throw new Error(`User with UUID ${userId} not found`);
      }

      await this.database.user.update({
        where: { uuid: userId },
        data: { valid_until: new Date(validUntil) },
      });

      this.logger.log(
        `UserService - ValidUntil date updated successfully for userId: ${userId}`,
      );
      return 'ValidUntil date saved successfully';
    } catch (error) {
      this.logger.error(
        `UserService - Error updating ValidUntil date for userId: ${userId}:`,
        error,
      );
      throw new Error('Failed to update ValidUntil date');
    }
  }
  async updateChangeFlag(
    userUuid: string,
    changeFlag: boolean,
  ): Promise<string> {
    try {
      const user = await this.database.user.findUnique({
        where: { uuid: userUuid },
      });

      if (!user) {
        throw new Error(`User with UUID ${userUuid} not found`);
      }

      const userAdditionalInfo =
        await this.database.user_additional_info.findUnique({
          where: { user_id: user.id },
        });
      if (!userAdditionalInfo) {
        throw new Error(`UserAdditionalInfo for User ID ${user.id} not found`);
      }
      await this.database.user_additional_info.update({
        where: { user_id: user.id },
        data: { change_flag: changeFlag },
      });
      return 'Change flag updated successfully';
    } catch (error) {
      this.logger.error('Error updating change flag:', error);
      throw new Error('Failed to update change flag');
    }
  }
  async updateReadCalibrationFromBackend(
    userId: string,
    readCalibrationFromBackend: boolean,
  ): Promise<string> {
    try {
      const user = await this.database.user.findUnique({
        where: { uuid: userId },
      });

      if (!user) {
        throw new Error(`User with UUID ${userId} not found`);
      }

      await this.database.user_additional_info.update({
        where: { user_id: user.id },
        data: { read_calibration_from_backend: readCalibrationFromBackend },
      });

      return 'ReadCalibrationFromBackend flag updated successfully';
    } catch (error) {
      this.logger.error(
        'Error updating ReadCalibrationFromBackend flag:',
        error,
      );
      throw new Error('Failed to update ReadCalibrationFromBackend flag');
    }
  }
  async updateUserFlag(userUuid: string, flag: boolean): Promise<string> {
    try {
      // Find the user by UUID
      const user = await this.database.user.findUnique({
        where: { uuid: userUuid },
      });

      if (!user) {
        throw new Error(`User with UUID ${userUuid} not found`);
      }
      const userAdditionalInfo =
        await this.database.user_additional_info.findUnique({
          where: { user_id: user.id },
        });
      if (!userAdditionalInfo) {
        throw new Error(`UserAdditionalInfo for User ID ${user.id} not found`);
      }
      // Update the flag in user_additional_info
      await this.database.user_additional_info.update({
        where: { user_id: user.id },
        data: { change_flag: flag },
      });

      return 'Flag updated successfully';
    } catch (error) {
      console.error('Error updating flag:', error.message);
      throw new Error('Failed to update flag');
    }
  }

  async resetPerformedTestFlag(userUUID: string): Promise<string> {
    try {
      this.logger.log(
        `Resetting performed_test_today flag for user ${userUUID}`,
      );

      const user = await this.database.user.findUnique({
        where: { uuid: userUUID },
        select: { id: true },
      });

      if (!user) {
        this.logger.error(`User with UUID ${userUUID} not found.`);
        throw new Error(`User with UUID ${userUUID} not found`);
      }

      await this.database.user_additional_info.update({
        where: { user_id: user.id },
        data: { performed_test_today: false },
      });

      this.logger.log(
        `Successfully reset performed_test_today flag for user ${userUUID}`,
      );
      return 'Performed test flag reset successfully';
    } catch (error) {
      this.logger.error(
        `Error resetting performed_test_today flag for user ${userUUID}:`,
        error,
      );
      throw new Error('Failed to reset performed test flag');
    }
  }

  async updateUserField(
    userUUID: string,
    field: string,
    value: any,
  ): Promise<string> {
    try {
      // Fetch the user ID from the user table
      const user = await this.database.user.findUnique({
        where: { uuid: userUUID },
        select: { id: true },
      });

      if (!user) {
        throw new Error(`User with UUID ${userUUID} not found.`);
      }

      // Define valid fields for each table
      const userFields = ['valid_until'];
      const additionalInfoFields = [
        'performed_test_today',
        'change_flag',
        'read_calibration_from_backend',
        'has_double_vision',
        'starred',
      ];

      if (userFields.includes(field)) {
        // Update the `user` table
        await this.database.user.update({
          where: { uuid: userUUID },
          data: { [field]: field === 'valid_until' ? new Date(value) : value },
        });
      } else if (additionalInfoFields.includes(field)) {
        // Update the `user_additional_info` table
        await this.database.user_additional_info.update({
          where: { user_id: user.id },
          data: { [field]: value },
        });
      } else {
        throw new Error(`Invalid field: ${field}`);
      }

      return `Field '${field}' updated successfully.`;
    } catch (error) {
      console.error(
        `Error updating field '${field}' for user ${userUUID}:`,
        error,
      );
      throw new Error(`Failed to update field '${field}'`);
    }
  }

  createDeviceChecksum = (data: {
    deviceId: string;
    height?: number;
    width?: number;
    model?: string;
    os?: string;
  }): string => {
    const input = `${data.deviceId || ''}_${data.height || ''}_${
      data.width || ''
    }_${data.model || ''}_${data.os || ''}`;
    return CryptoJS.SHA256(input).toString();
  };

  async updateDeviceData(
    userUuid: string,
    updateData: UpdateDeviceDataDto,
  ): Promise<{ message: string; deviceData: any }> {
    // Find the user by its UUID
    const user = await this.database.user.findUnique({
      where: { uuid: userUuid },
    });

    if (!user) {
      throw new HttpException('User not found', HttpStatus.NOT_FOUND);
    }

    // Compute the device checksum based on the provided updateData
    const computedChecksum = this.createDeviceChecksum({
      deviceId: updateData.deviceId,
      height: updateData.height,
      width: updateData.width,
      model: updateData.model,
      os: updateData.os,
    });

    // Check if a device_data record already exists for this user with the same checksum
    const existingDeviceData = await this.database.device_data.findFirst({
      where: {
        userId: user.id,
        deviceChecksum: computedChecksum,
      },
    });

    if (existingDeviceData) {
      // Return the record in the desired structure.
      return {
        message: 'Device data already exists.',
        deviceData: {
          ...existingDeviceData,
          dimensions: {
            height: existingDeviceData.height,
            pixelDensity: existingDeviceData.pixelDensity,
            width: existingDeviceData.width,
            diagonalLength: existingDeviceData.diagonalLength,
          },
        },
      };
    } else {
      // Otherwise, create a new record with the computed checksum.
      const deviceDataRecord = await this.database.device_data.create({
        data: {
          userId: user.id,
          deviceId: updateData.deviceId,
          deviceOffset: updateData.deviceOffset,
          height: updateData.height,
          pixelDensity: updateData.pixelDensity,
          width: updateData.width,
          mmfor1Pixel: updateData.mmfor1Pixel,
          model: updateData.model,
          os: updateData.os,
          pixelfor1mm: updateData.pixelfor1mm,
          diagonalLength: updateData.diagonalLength,
          deviceChecksum: computedChecksum,
        },
      });
      return {
        message: 'Device data created successfully.',
        deviceData: {
          ...deviceDataRecord,
          dimensions: {
            height: deviceDataRecord.height,
            pixelDensity: deviceDataRecord.pixelDensity,
            width: deviceDataRecord.width,
            diagonalLength: deviceDataRecord.diagonalLength,
          },
        },
      };
    }
  }
  async addTrainingConfig(
    userUUID: string,
    trainingConfigDto: TrainingConfigDto,
  ): Promise<string> {
    try {
      const user = await this.database.user.findUnique({
        where: { uuid: userUUID },
      });

      if (!user) {
        throw new Error(`User with uuid ${userUUID} not found`);
      }

      const result = await this.database.training_config.create({
        data: {
          ...trainingConfigDto,
          user_id: Number(user.id),
        },
      });

      this.logger.log(`Training config saved successfully for user ${user.id}`);
      return 'Training config saved successfully';
    } catch (error) {
      this.logger.error('Error saving training config:', error);
      throw new Error('Failed to save training config');
    }
  }
  async getTeachersForStudent(
    studentUuid: string,
  ): Promise<
    Array<{ firstName: string; lastName: string; teacher_uuid: string }>
  > {
    // 1. Find the user by UUID from the user table.
    const userRecord = await this.database.user.findUnique({
      where: { uuid: studentUuid },
      select: { id: true },
    });
    if (!userRecord) {
      throw new Error(`User with UUID ${studentUuid} not found`);
    }
    // userRecord.id is the user table id, which is different from the student id.

    // 2. Find the corresponding student record in the student table
    // by matching userRecord.id with the user_id column.
    const studentRecord = await this.database.student.findFirst({
      where: { userId: userRecord.id },
      select: { id: true },
    });
    if (!studentRecord) {
      throw new Error(`Student record for user id ${userRecord.id} not found`);
    }
    const studentId = studentRecord.id;

    // 3. Get all rows from student_teacher where student_id equals the student id.
    const studentTeacherRecords = await this.database.studentTeacher.findMany({
      where: { studentId: studentId },
      select: { teacherId: true },
    });
    if (!studentTeacherRecords || studentTeacherRecords.length === 0) {
      throw new Error(
        `No teacher associations found for student with id ${studentId}`,
      );
    }

    // 4. For each teacher_id, retrieve the teacher's info.
    const teachers = [];
    for (const record of studentTeacherRecords) {
      // Retrieve teacher record from teacher table using teacherId.
      const teacher = await this.database.teacher.findUnique({
        where: { id: record.teacherId },
        select: { first_name: true, last_name: true, teacher_uuid: true },
      });
      if (!teacher) continue;

      teachers.push({
        firstName: teacher.first_name,
        lastName: teacher.last_name,
        teacher_uuid: teacher.teacher_uuid,
      });
    }

    return teachers;
  }

  async recoverUsername(
    recoverUsernameDto: RecoverUsernameDto,
  ): Promise<boolean> {
    const apiName = '[recoverUsername API]';
    const first_name = recoverUsernameDto.first_name.trim();
    const last_name = recoverUsernameDto.last_name.trim();
    const { birthdate, language } = recoverUsernameDto;
    this.logger.log(
      `${apiName} Looking up additional info for ${first_name} ${last_name}, birthdate ${birthdate}`,
    );

    // Convert the incoming birthdate to a Date object.
    const parsedBirthdate = new Date(birthdate);

    // Step 1: Find the child's additional info in user_additional_info.
    const childAdditionalInfo =
      await this.database.user_additional_info.findFirst({
        where: { first_name, last_name, birthdate: parsedBirthdate },
        select: { email: true, user_id: true },
      });

    if (!childAdditionalInfo) {
      throw new NotFoundException('No matching child record found.');
    }
    this.logger.log(
      `${apiName} Found child email: ${childAdditionalInfo.email}`,
    );

    // Step 2: Using the user_id, get the child's user record.
    const childUserRecord = await this.database.user.findUnique({
      where: { id: childAdditionalInfo.user_id },
      select: { parent_info_id: true },
    });
    if (!childUserRecord) {
      throw new NotFoundException('Child user record not found.');
    }

    // Step 3: Get the parent's email using parent_info_id.
    const parentInfo = await this.database.parent_info.findUnique({
      where: { id: childUserRecord.parent_info_id },
      select: { email: true },
    });
    if (!parentInfo) {
      throw new NotFoundException('Parent record not found.');
    }
    this.logger.log(`${apiName} Found parent email: ${parentInfo.email}`);

    // Step 4: Derive child's username from the child's email.
    const childUsername = childAdditionalInfo.email.split('@')[0];
    this.logger.log(`${apiName} Derived child username: ${childUsername}`);

    // Step 5: Generate a reset password link for the child's email.
    const auth = this.firebaseService.getAuth();
    const resetPasswordLink = await auth.generatePasswordResetLink(
      childAdditionalInfo.email,
    );

    // Step 6: Build email templates using variables (for localization purposes).
    let messages = {
      recoverUsername: {
        subject: 'IMVI Recover Username',
        text: "Your child's username is: {username}. You can reset your password using this link: {resetLink}",
        html: "<h3>Your child's username is: {username}</h3><p>Reset your password <a href='{resetLink}'>here</a>.</p>",
      },
    };
    if (language.toLocaleLowerCase() == 'sv') {
      messages = {
        recoverUsername: {
          subject: 'IMVI Förlorat Användarnamn',
          text: 'Ditt barns användarnamn är: {username}. Du kan återställa ditt lösenord med hjälp av den här länken: {resetLink}',
          html: "<h3>Ditt barns användarnamn är: {username}</h3><p>Återställ ditt lösenord <a href='{resetLink}'>här</a>.</p>",
        },
      };
    }
    // Replace placeholders with actual values.
    const subject = messages.recoverUsername.subject;
    const textBody = messages.recoverUsername.text
      .replace('{username}', childUsername)
      .replace('{resetLink}', resetPasswordLink);
    const htmlBody = messages.recoverUsername.html
      .replace('{username}', childUsername)
      .replace('{resetLink}', resetPasswordLink);

    // Step 7: Send the email to the parent's email address.
    await this.mailService.sendMail(
      parentInfo.email,
      '',
      subject,
      textBody,
      htmlBody,
    );
    this.logger.log(
      `${apiName} Username and reset link email sent to parent: ${parentInfo.email}`,
    );

    return true;
  }
  async createUser(dto: CreateUserDto) {
    try {
      // Always create a new user; assume uuid is null for new users.
      const user = await this.database.user.create({
        data: {
          uuid: null,
          registered_on: new Date(),
          starred: false,
          type: 'standard',
          deleted: false,
          additional_info: {
            create: {
              name: dto.name,
              email: dto.email,
              age: dto.age ? Number(dto.age) : null,
              vision_problem: dto.vision_problem
                ? dto.vision_problem.join(',')
                : null,
              optional_text: dto.optional_text,
              accept_newsletter: dto.accept_newsletter || false,
              training_question_id: 1, // Default value
            },
          },
        },
      });
      return user;
    } catch (error) {
      throw new HttpException(
        'Error adding user',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  async getSurveyAnswersByUuid(
    userUuid: string,
  ): Promise<{ message: string; success: boolean; data?: any }> {
    // 1. Find the user by UUID from the user table.
    const userRecord = await this.database.user.findUnique({
      where: { uuid: userUuid },
      select: { id: true },
    });
    if (!userRecord) {
      throw new Error(`User with UUID ${userUuid} not found`);
    }

    // 2. Retrieve survey records for the user.
    // Assume the user_survey table has columns: user_id, question_index, answer_index.
    const surveyRecords = await this.database.userSurvey.findMany({
      where: { user_id: userRecord.id },
      select: { question_index: true, answer_index: true },
    });
    if (!surveyRecords || surveyRecords.length === 0) {
      throw new Error(
        `No survey records found for user with id ${userRecord.id}`,
      );
    }

    // 3. Hardcode the survey questions.
    const surveyQuestions = [
      'Do you experience any of the following?',
      'Have you had any of the following?',
    ];

    // 4. Hardcode the answer options.
    // Answer options for the first question:
    const firstQuestionOptions = [
      { id: 1, label: 'Eye strain', value: 'eye-strain' },
      { id: 2, label: 'Double vision', value: 'double-vision' },
      { id: 3, label: 'Reading difficulties', value: 'reading-difficulties' },
      {
        id: 4,
        label: 'Concentration difficulties',
        value: 'concentration-difficulties',
      },
      { id: 5, label: 'None', value: 'none' },
      { id: 6, label: 'Rather not say', value: 'rather-not-say' },
    ];

    // Answer options for the second question:
    const secondQuestionOptions = [
      {
        id: 1,
        label: 'Vergence Insufficiency',
        value: 'vergence-insufficiency',
      },
      { id: 2, label: 'Dyslexia diagnosis', value: 'dyslexia-diagnosis' },
      { id: 3, label: 'ADHD diagnosis', value: 'adhd-diagnosis' },
      { id: 4, label: 'Stroke', value: 'stroke' },
      { id: 5, label: 'Concussion', value: 'concussion' },
      { id: 6, label: 'Post-covid', value: 'post-covid' },
      { id: 7, label: 'None', value: 'none' },
      { id: 8, label: 'Rather not say', value: 'rather-not-say' },
    ];

    const answerOptions = [firstQuestionOptions, secondQuestionOptions];

    // 5. Map the survey records to a readable structure.
    const surveyResults = surveyRecords.map((record) => {
      const questionIndex = record.question_index - 1; // Convert to 0-indexed.
      const question = surveyQuestions[questionIndex] || null;
      const answerOption = (answerOptions[questionIndex] || []).find(
        (opt) => opt.id === record.answer_index,
      );
      return {
        question,
        answer: answerOption ? answerOption.label : null,
      };
    });

    return {
      message: 'Survey answers retrieved successfully.',
      success: true,
      data: surveyResults,
    };
  }

  async getTrainingQuestions(
    userUuid: string,
  ): Promise<{ message: string; success: boolean; data?: any }> {
    // 1. Find the user by UUID.
    const userRecord = await this.database.user.findUnique({
      where: { uuid: userUuid },
      select: { id: true },
    });
    if (!userRecord) {
      throw new Error(`User with UUID ${userUuid} not found`);
    }

    // 2. Retrieve training question records for the user from the postTrainingData model.
    const trainingQuestions = await this.database.postTrainingData.findMany({
      where: { user_id: userRecord.id },
      select: {
        question_id: true,
        question: true,
        answer_id: true,
        answer: true,
        created_at: true,
      },
    });
    if (!trainingQuestions || trainingQuestions.length === 0) {
      throw new Error(
        `No training question data found for user with id ${userRecord.id}`,
      );
    }

    return {
      message: 'Training questions retrieved successfully.',
      success: true,
      data: trainingQuestions,
    };
  }

  async getUserByEmail(
    email: string,
  ): Promise<{ userId: number; uuid: string } | null> {
    const apiName = '[getUserByEmail API]';
    try {
      this.logger.log(`${apiName} Looking up users by email: ${email}`);

      // Find all user_additional_info records with the given email
      const userAdditionalInfo =
        await this.database.user_additional_info.findFirst({
          where: { email: email },
          include: {
            user: {
              select: {
                id: true,
                uuid: true,
              },
            },
          },
        });

      if (!userAdditionalInfo || !userAdditionalInfo.user) {
        this.logger.warn(`${apiName} No users found with email: ${email}`);
        return null;
      }

      this.logger.log(
        `${apiName} Found user with ID: ${userAdditionalInfo.user.id}, UUID: ${
          userAdditionalInfo.user.uuid || 'null'
        }`,
      );

      return {
        uuid: userAdditionalInfo.user.uuid,
        userId: userAdditionalInfo.user.id,
      };
    } catch (error) {
      this.logger.error(
        `${apiName} Error getting user by email: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
