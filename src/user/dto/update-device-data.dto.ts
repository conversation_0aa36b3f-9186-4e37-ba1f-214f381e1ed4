import { IsOptional, IsString, IsNumber } from 'class-validator';

export class UpdateDeviceDataDto {
  @IsString()
  deviceId: string;

  @IsOptional()
  @IsNumber()
  deviceOffset?: number;

  @IsOptional()
  @IsNumber()
  height?: number;

  @IsOptional()
  @IsNumber()
  pixelDensity?: number;

  @IsOptional()
  @IsNumber()
  width?: number;

  @IsOptional()
  @IsNumber()
  mmfor1Pixel?: number;

  @IsOptional()
  @IsString()
  model?: string;

  @IsOptional()
  @IsString()
  os?: string;

  @IsOptional()
  @IsNumber()
  pixelfor1mm?: number;

  @IsOptional()
  @IsNumber()
  diagonalLength?: number;
}
