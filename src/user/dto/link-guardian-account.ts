import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsUUID } from 'class-validator';

type GuardianInfo = {
  first_name: string;
  last_name: string;
  email: string;
};

export class LinkGuardianAccountDto {
  @ApiProperty({ description: 'Unique identifier of the child account' })
  @IsNotEmpty()
  readonly childAccountUUID: string;

  @ApiProperty({ description: 'Guardian info' })
  @IsNotEmpty()
  readonly guardianInfo: GuardianInfo;
}
