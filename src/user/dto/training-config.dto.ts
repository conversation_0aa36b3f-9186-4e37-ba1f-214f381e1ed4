import { ApiProperty } from "@nestjs/swagger";

import { IsNotEmpty } from "class-validator";

export class TrainingConfigDto {
    @ApiProperty({ description: 'Left Eye Offset X' })
    @IsNotEmpty()
    readonly left_eye_offset_x: number;

    @ApiProperty({ description: 'Left Eye Offset Y' })
    @IsNotEmpty()
    readonly left_eye_offset_y: number;

    @ApiProperty({ description: 'Left Eye Speed X' })
    @IsNotEmpty()
    readonly left_eye_speed_x: number;

    @ApiProperty({ description: 'Left Eye Speed Y' })
    @IsNotEmpty()
    readonly left_eye_speed_y: number;

    @ApiProperty({ description: 'Left Eye Pendulum X' })
    @IsNotEmpty()
    readonly left_eye_pendulum_x: number;

    @ApiProperty({ description: 'Left Eye Pendulum Y' })
    @IsNotEmpty()
    readonly left_eye_pendulum_y: number;

    @ApiProperty({ description: 'Right Eye Offset X' })
    @IsNotEmpty()
    readonly right_eye_offset_x: number;

    @ApiProperty({ description: 'Right Eye Offset Y' })
    @IsNotEmpty()
    readonly right_eye_offset_y: number;

    @ApiProperty({ description: 'Right Eye Speed X' })
    @IsNotEmpty()
    readonly right_eye_speed_x: number;

    @ApiProperty({ description: 'Right Eye Speed Y' })
    @IsNotEmpty()
    readonly right_eye_speed_y: number;

    @ApiProperty({ description: 'Right Eye Pendulum X' })
    @IsNotEmpty()
    readonly right_eye_pendulum_x: number;

    @ApiProperty({ description: 'Right Eye Pendulum Y' })
    @IsNotEmpty()
    readonly right_eye_pendulum_y: number;
}