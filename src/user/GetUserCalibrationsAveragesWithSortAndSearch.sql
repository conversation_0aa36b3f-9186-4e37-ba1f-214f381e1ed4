CREATE DEFINER=`imvi`@`%` PROCEDURE `imviread`.`GetUserCalibrationsAveragesWithSortAndSearch`(
    IN sort_field VARCHAR(50), 
    IN sort_order VARCHAR(4), 
    IN search_query VARCHAR(255),
    IN limit_results INT, 
    IN offset_results INT,
    IN filter_type VARCHAR(255)
)
BEGIN
    -- Validate sort order
    IF sort_order NOT IN ('ASC', 'DESC') THEN
        SET sort_order = 'ASC';
    END IF;

    -- Validate sort field
    SET sort_field = CASE 
        WHEN sort_field IN (
            'latest_average', 
            'previous_three_average', 
            'email', 
            'first_name', 
            'last_name', 
            'registered_on', 
            'standard_deviation', 
            'calibration_standard_deviation', 
            'first_training_date', 
            'last_training_date', 
            'next_training_date',
            'valid_until',
            'vergence_test_count',
            'performed_test_today',
            'has_double_vision'
        ) THEN sort_field
        ELSE 'registered_on'
    END;

    -- Build filter condition based on filter_type parameter.
    IF filter_type LIKE '%all%' THEN
        SET @filterCondition = '';
    ELSE
        SET @filterCondition = ' AND (';
        -- Adjusted Training filter: if Training is ticked but student is not, exclude those training users who are students.
        IF FIND_IN_SET('Training', filter_type) > 0 THEN
            IF FIND_IN_SET('student', filter_type) = 0 THEN
                SET @filterCondition = CONCAT(
                  @filterCondition,
                  " (u.type IN ('Training', 'standard') AND NOT EXISTS (SELECT 1 FROM student s WHERE s.user_id = u.id)) "
                );
            ELSE
                SET @filterCondition = CONCAT(
                  @filterCondition,
                  " u.type IN ('Training', 'standard') "
                );
            END IF;
        END IF;
        IF FIND_IN_SET('Guardian', filter_type) > 0 THEN
            IF LENGTH(@filterCondition) > LENGTH(' AND (') THEN
                SET @filterCondition = CONCAT(@filterCondition, " OR ");
            END IF;
            SET @filterCondition = CONCAT(@filterCondition, " u.type = 'Guardian' ");
        END IF;
        IF FIND_IN_SET('performedTests', filter_type) > 0 THEN
            IF LENGTH(@filterCondition) > LENGTH(' AND (') THEN
                SET @filterCondition = CONCAT(@filterCondition, " OR ");
            END IF;
            SET @filterCondition = CONCAT(@filterCondition, " ua.performed_test_today = 1 ");
        END IF;
        IF FIND_IN_SET('child', filter_type) > 0 THEN
            IF LENGTH(@filterCondition) > LENGTH(' AND (') THEN
                SET @filterCondition = CONCAT(@filterCondition, " OR ");
            END IF;
            SET @filterCondition = CONCAT(@filterCondition, " ua.email LIKE '%@imvilabs-child.com' ");
        END IF;
        IF FIND_IN_SET('student', filter_type) > 0 THEN
            IF LENGTH(@filterCondition) > LENGTH(' AND (') THEN
                SET @filterCondition = CONCAT(@filterCondition, " OR ");
            END IF;
            SET @filterCondition = CONCAT(@filterCondition, " u.id IN (SELECT user_id FROM student) ");
        END IF;
        IF FIND_IN_SET('starred', filter_type) > 0 THEN
            IF LENGTH(@filterCondition) > LENGTH(' AND (') THEN
                SET @filterCondition = CONCAT(@filterCondition, " OR ");
            END IF;
            SET @filterCondition = CONCAT(@filterCondition, " ua.starred = 1 ");
        END IF;
        SET @filterCondition = CONCAT(@filterCondition, ")");
    END IF;

    SET @query = CONCAT(
       "SELECT 
            u.id AS user_id, 
            u.uuid AS user_uuid, 
            u.registered_on AS registered_on, 
            ua.email AS email, 
            ua.first_name AS first_name, 
            ua.last_name AS last_name, 
            MAX(CASE WHEN sub.row_index = 1 THEN sub.avg_value END) AS latest_average,  
            previous_three.avg_value AS previous_three_average,  
            CASE 
              WHEN MAX(CASE WHEN sub.row_index = 1 THEN sub.avg_value END) IS NOT NULL 
                   AND MAX(CASE WHEN sub.row_index = 1 THEN sub.avg_value END) <> 0 
              THEN ROUND(last_five.stddev_last_five, 2)
              ELSE NULL
            END AS standard_deviation,  
            CASE 
              WHEN previous_three.avg_value IS NULL THEN NULL
              ELSE calibration_stddev.stddev_value
            END AS calibration_standard_deviation,  
            (SELECT MIN(tsd.start_time)
             FROM imviread.training_session_data tsd
             WHERE tsd.user_id = u.id) AS first_training_date,           
            (SELECT MAX(tsd.start_time)
             FROM imviread.training_session_data tsd
             WHERE tsd.user_id = u.id) AS last_training_date,            
            (SELECT MIN(st.scheduled_datetime)
             FROM imviread.scheduled_test st
             WHERE st.user_id = u.id AND st.scheduled_datetime > NOW()) AS next_training_date,  
            (SELECT COUNT(*)
             FROM imviread.vergence_user_session vus
             WHERE vus.user_id = u.id) AS vergence_test_count,           
            CAST(u.valid_until AS DATETIME) AS valid_until,               
            ua.change_flag AS change_flag,                               
            ua.read_calibration_from_backend AS read_calibration_from_backend,  
            ua.performed_test_today AS performed_test_today,             
            ua.has_double_vision AS has_double_vision,                   
            COUNT(*) OVER() AS total_results,                            
            last_five.last_five_calibrations,                            
            u.type AS type,                                              
            ua.starred AS starred                                        
        FROM 
            imviread.user u 
        INNER JOIN imviread.user_additional_info ua ON ua.user_id = u.id 
        LEFT JOIN (
            SELECT user_id, AVG(avg_value) AS avg_value
            FROM (
                SELECT
                    c.user_id,
                    AVG(cd.value) AS avg_value,
                    ROW_NUMBER() OVER (PARTITION BY c.user_id ORDER BY c.calibration_date DESC) AS row_index
                FROM imviread.calibration c
                JOIN imviread.calibration_data cd ON c.id = cd.calibration_id
                GROUP BY c.id, c.user_id, c.calibration_date
            ) ranked
            WHERE row_index BETWEEN 2 AND 4
            GROUP BY user_id
        ) previous_three ON previous_three.user_id = u.id 
        LEFT JOIN (
            SELECT 
                c.user_id, 
                ROUND(STDDEV_POP(cd.value), 2) AS stddev_value
            FROM imviread.calibration c
            JOIN imviread.calibration_data cd ON c.id = cd.calibration_id
            JOIN (
                SELECT 
                    c1.user_id, 
                    c1.id AS latest_calibration_id
                FROM imviread.calibration c1
                WHERE c1.calibration_date = (
                    SELECT MAX(c2.calibration_date)
                    FROM imviread.calibration c2
                    WHERE c2.user_id = c1.user_id
                )
            ) latest 
              ON c.user_id = latest.user_id 
             AND c.id = latest.latest_calibration_id
            GROUP BY c.user_id
        ) calibration_stddev ON calibration_stddev.user_id = u.id
        LEFT JOIN (
            SELECT *
            FROM (
                SELECT
                    c.id AS calibration_id,
                    c.user_id,
                    c.calibration_date,
                    AVG(cd.value) AS avg_value,
                    ROW_NUMBER() OVER (PARTITION BY c.user_id ORDER BY c.calibration_date DESC) AS row_index
                FROM imviread.calibration c
                LEFT JOIN imviread.calibration_data cd ON c.id = cd.calibration_id
                JOIN (
                    SELECT 
                        user_id, 
                        DATE(calibration_date) AS calibration_day, 
                        MAX(calibration_date) AS latest_calibration
                    FROM imviread.calibration
                    GROUP BY user_id, DATE(calibration_date)
                ) latest 
                  ON c.user_id = latest.user_id
                 AND DATE(c.calibration_date) = latest.calibration_day
                 AND c.calibration_date = latest.latest_calibration
                GROUP BY c.id, c.user_id, c.calibration_date
            ) ranked
            WHERE row_index = 1
        ) sub ON sub.user_id = u.id 
        LEFT JOIN (
            SELECT 
                user_id,
                SUBSTRING_INDEX(GROUP_CONCAT(avg_value ORDER BY calibration_date DESC SEPARATOR ', '), ', ', 5) AS last_five_calibrations,
                STDDEV_POP(avg_value) AS stddev_last_five
            FROM (
                SELECT 
                    c.user_id,
                    c.calibration_date,
                    AVG(cd.value) AS avg_value,
                    ROW_NUMBER() OVER (PARTITION BY c.user_id ORDER BY c.calibration_date DESC) AS row_index
                FROM imviread.calibration c
                JOIN imviread.calibration_data cd ON c.id = cd.calibration_id
                GROUP BY c.id, c.calibration_date, c.user_id
            ) t
            WHERE row_index BETWEEN 1 AND 5
            GROUP BY user_id
        ) last_five ON last_five.user_id = u.id 
        WHERE (sub.row_index <= 5 OR sub.row_index IS NULL) ", @filterCondition, "
        GROUP BY 
            u.id, u.uuid, ua.email, ua.first_name, ua.last_name, u.registered_on, 
            ua.change_flag, ua.performed_test_today, ua.has_double_vision
        HAVING 
            ua.first_name LIKE ? OR ua.last_name LIKE ? OR ua.email LIKE ?
        ORDER BY ", sort_field, " ", sort_order, " 
        LIMIT ? OFFSET ?"
    );

    PREPARE stmt FROM @query;
    SET @search_param = CONCAT('%', search_query, '%');
    EXECUTE stmt USING @search_param, @search_param, @search_param, limit_results, offset_results;
    DEALLOCATE PREPARE stmt;
END
