import { initializeApp } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { getAuth } from 'firebase-admin/auth';
import { getMessaging } from 'firebase-admin/messaging';

import { Injectable, OnModuleInit } from '@nestjs/common';

@Injectable()
export class FirebaseService implements OnModuleInit {
  private firestore: any;
  private auth: any;
  private messaging: any;

  onModuleInit() {
    const imviConfig = JSON.parse(process.env.IMVI_FIREBASE_CONFIG);

    let imviApp = initializeApp(imviConfig);

    this.firestore = getFirestore();
    this.auth = getAuth();
    this.messaging = getMessaging();
  }

  getFirestore(): any {
    return this.firestore;
  }

  getAuth(): any {
    return this.auth;
  }

  getMessaging(): any {
    return this.messaging;
  }

  async disableUser(uuid: string): Promise<void> {
    try {
      await this.auth.updateUser(uuid, { disabled: true });
      console.log(`Successfully disabled user with UUID: ${uuid}`);
    } catch (error) {
      console.error(`Error disabling user with UUID: ${uuid}`, error);
      throw error;
    }
  }

  // New: Verifying a Firebase Auth token
  async verifyToken(token: string): Promise<any> {
    try {
      return await this.auth.verifyIdToken(token);
    } catch (error) {
      console.error('Error verifying Firebase token', error);
      throw new Error('Unauthorized');
    }
  }
}
