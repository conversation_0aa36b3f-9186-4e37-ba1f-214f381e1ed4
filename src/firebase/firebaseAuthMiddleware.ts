import {
  Injectable,
  NestMiddleware,
  UnauthorizedException,
} from '@nestjs/common';
import { FirebaseService } from './firebase.service';

@Injectable()
export class AuthMiddleware implements NestMiddleware {
  constructor(private readonly firebaseService: FirebaseService) {}

  async use(req: any, res: any, next: () => void) {
    // const token = req.headers.authorization?.split('Bearer ')[1];
    // if (!token) {
    //   throw new UnauthorizedException('Token is missing');
    // }

    // try {
    //   const decodedToken = await this.firebaseService.verifyToken(token);
    next();
    // } catch (error) {
    //  throw new UnauthorizedException('Invalid token');
    //   }
    // }
  }
}
