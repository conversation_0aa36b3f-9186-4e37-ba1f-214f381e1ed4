import { Injectable, NotFoundException } from '@nestjs/common';
import { DatabaseService } from 'src/database/database.service';
import { SchoolDto } from './dto/school-dto';
import { SchoolClassDto } from './dto/school-class-dto';
import { CreateStudentDto } from './dto/create-student-dto';
import { ConfigService } from '@nestjs/config';
import {
  generateCode,
  generateOrderNumber,
  generatePassword,
} from './codeGenerator';
import { CreateTeacherDto } from './dto/create-teacher-dto';
import { FirebaseService } from 'src/firebase/firebase.service';
import { MailService } from 'src/mail/mail.service';
import { UpdateStudentDto } from './dto/update-student-dto';
import { teacher, student } from '@prisma/client';
import * as PDFDocument from 'pdfkit';
import { ChartJSNodeCanvas } from 'chartjs-node-canvas';
import * as fs from 'fs';
import { PassThrough } from 'stream';
import { VergenceTestsService } from 'src/vergenceTest/vergence_tests.service';
import moment = require('moment');
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import { ChartTypeRegistry, ChartConfiguration } from 'chart.js';
import { start } from 'repl';
import { report } from 'process';

import { pipeline } from 'stream/promises';
import { Writable } from 'stream';
import { CreateSchoolDto } from './dto/create-school-dto';
import { CreateClassDto } from './dto/create-class-dto';

type ValidChartType = keyof ChartTypeRegistry;

enum ReportFrequency {
  None = 0,
  Daily = 1,
  Weekly = 2,
  Monthly = 3,
}

interface Student {
  id: number;
  first_name: string;
  last_name: string;
  user: {
    uuid: string;
    additional_info: any;
    // Add other relevant fields from the user model
  };
}

interface Teacher {
  id: number;
  first_name: string;
  last_name: string;
  // Add other relevant fields if needed
}

interface VergenceData {
  headers: Header[];
  results: any[];
  improvementsHeaders: Header[];
  improvements: any[];
  datasetKeys: string[];
  dataset: number[];
}

interface ReadingData {
  headers: Header[];

  lettersResults: any[];
  wordsResults: any[];
  sentencesResults: any[];

  improvementsHeaders: Header[];
  wordsImprovements: any[];
  lettersImprovements: any[];
  sentencesImprovements: any[];

  datasetKeys: string[];
  lettersDataset: number[];
  wordsDataset: number[];
  sentencesDataset: number[];
}

interface Coordinates {
  x: number;
  y: number;
}

interface Header {
  content: string;
  width: number;
}

const tableTop = 150;
const rowHeight = 20;

@Injectable()
export class SchoolService {
  private secretKey: string;

  constructor(
    private database: DatabaseService,
    private configService: ConfigService,
    private firebaseService: FirebaseService,
    private mailService: MailService,
    private vergenceService: VergenceTestsService,
    private httpService: HttpService,
  ) {
    this.secretKey = this.configService.get<string>('SECRET_KEY');
  }

  async getAllSchools(): Promise<{ schools: SchoolDto[]; teachers: any[] }> {
    const schools = await this.database.school.findMany({
      include: {
        classes: true, // Include related classes
      },
    });

    const schoolDtos = schools.map((school) => ({
      id: school.id,
      name: school.name,
      city: school.city,
      classes: school.classes.map((schoolClass) => ({
        id: schoolClass.id,
        name: schoolClass.name,
        schoolId: schoolClass.schoolId,
      })),
    }));

    const teachers = await this.database.teacher.findMany();

    const teacherDtos = teachers.map((teacher) => ({
      id: teacher.id,
      first_name: teacher.first_name,
      last_name: teacher.last_name,
    }));

    return { schools: schoolDtos, teachers: teacherDtos };
  }

  async getClassesBySchoolId(
    schoolId: number,
  ): Promise<{ classes: SchoolClassDto[] }> {
    const classes = await this.database.schoolClass.findMany({
      where: { schoolId },
    });
    const classDtos = classes.map((schoolClass) => ({
      id: schoolClass.id,
      name: schoolClass.name,
      schoolId: schoolClass.schoolId,
    }));
    return { classes: classDtos };
  }

  async createTeacher(createTeacherDto: CreateTeacherDto): Promise<any> {
    const {
      first_name,
      last_name,
      phone_number,
      email,
      role,
      schoolId,
      classId,
    } = createTeacherDto;

    let password = generatePassword();
    const auth = this.firebaseService.getAuth();
    const db = this.firebaseService.getFirestore();
    const displayName = `${first_name} ${last_name}`;

    let teacherAccount;
    let isNewAccount = false;

    try {
      teacherAccount = await auth.createUser({
        email: email,
        password: password,
        displayName: displayName,
        disabled: false,
        emailVerified: true,
      });
      isNewAccount = true;
    } catch (error) {
      if (error.code === 'auth/email-already-exists') {
        teacherAccount = await auth.getUserByEmail(email);
      } else {
        throw error;
      }
    }

    await db.collection('SubAdminData').doc(teacherAccount.uid).set({
      Email: email,
      FirstName: first_name,
      LastName: last_name,
      MobileNumber: phone_number,
    });

    const customClaims = { teacher: true, role: role.toLowerCase() };
    await auth.setCustomUserClaims(teacherAccount.uid, customClaims);

    const teacherData: any = {
      first_name,
      last_name,
      teacher_uuid: teacherAccount.uid,
      role,
    };

    const teacher = await this.database.teacher.create({ data: teacherData });

    // Fetch all students in the specified class
    const studentsInClass = await this.database.student.findMany({
      where: { classId: classId },
      select: { id: true, user: { select: { uuid: true } } },
    });

    // Separate students with and without user UUIDs
    const studentsWithUserId = studentsInClass.filter(
      (student) => student.user?.uuid,
    );
    const studentsWithoutUserId = studentsInClass.filter(
      (student) => !student.user?.uuid,
    );

    // Create student-teacher links for all students in SQL
    const studentTeacherLinks = studentsInClass.map((student) => ({
      studentId: student.id,
      teacherId: teacher.id,
    }));

    await this.database.studentTeacher.createMany({
      data: studentTeacherLinks,
    });

    // Update Firestore's AssignedUsers for only the students with user UUIDs
    if (studentsWithUserId.length > 0) {
      const teacherRef = db.collection('SubAdminData').doc(teacherAccount.uid);
      const assignedUsers = studentsWithUserId.map((student) =>
        db.collection('UserData').doc(student.user.uuid),
      );

      await teacherRef.update({
        AssignedUsers: assignedUsers,
      });
    }

    if (isNewAccount) {
      const mailjet = this.mailService.getMailJet();
      await mailjet.post('send', { version: 'v3.1' }).request({
        Messages: [
          {
            From: { Email: '<EMAIL>', Name: 'IMVI' },
            To: [{ Email: email, Name: displayName }],
            TemplateID: 3706220,
            TemplateLanguage: true,
            Subject: 'IMVI Account Registration',
            Variables: {
              UserName: displayName,
              UserId: email,
              Password: password,
            },
          },
        ],
      });
    }

    return {
      message: isNewAccount ? 'Teacher Created' : 'Teacher Already Exists',
      data: teacherAccount,
    };
  }

  async createStudent(createStudentDto: CreateStudentDto): Promise<any> {
    const {
      userId,
      classId,
      teacherIds,
      first_name,
      last_name,
      birthdate,
      email,
      purchase_id,
    } = createStudentDto;

    const auth = this.firebaseService.getAuth();
    const db = this.firebaseService.getFirestore();

    // Check if a student with the same email already exists
    const existingStudent = await this.database.student.findUnique({
      where: { email },
      include: {
        studentTeachers: true, // Include the relationship with teachers
        user: {
          select: { uuid: true }, // Include the related user and fetch only the uuid
        },
      },
    });

    const studentUuid = existingStudent?.user?.uuid;

    if (existingStudent) {
      if (!studentUuid) {
        throw new Error('No UUID found for the student.');
      }
      const existingTeacherIds = existingStudent.studentTeachers.map(
        (st) => st.teacherId,
      );

      const newTeacherLinks = teacherIds.filter(
        (teacherId) => !existingTeacherIds.includes(teacherId),
      );

      if (newTeacherLinks.length > 0) {
        const studentTeacherRecords = newTeacherLinks.map((teacherId) => ({
          studentId: existingStudent.id,
          teacherId: teacherId,
        }));

        await this.database.studentTeacher.createMany({
          data: studentTeacherRecords,
        });
      }

      for (const teacherId of newTeacherLinks) {
        const teacher = await this.database.teacher.findUnique({
          where: { id: teacherId },
          select: { teacher_uuid: true },
        });

        if (!teacher || !teacher.teacher_uuid) {
          console.error(
            `No teacher UUID found for teacher with ID ${teacherId}`,
          );
          continue; // Skip to the next teacher if the teacher_uuid is missing
        }

        // Fetch the current `SubAdminData` from Firestore
        const teacherRef = db
          .collection('SubAdminData')
          .doc(teacher.teacher_uuid);
        const teacherDoc = await teacherRef.get();

        if (teacherDoc.exists) {
          const teacherData = teacherDoc.data();
          const assignedUsers = teacherData?.AssignedUsers || [];

          // Format the student reference as a Firestore document reference
          const studentRef = db.collection('UserData').doc(studentUuid);

          // Add the student's document reference if it's not already in the list
          if (!assignedUsers.includes(studentRef)) {
            assignedUsers.push(studentRef); // This will push the Firestore reference object
            await teacherRef.update({
              AssignedUsers: assignedUsers,
            });
          } else {
            // If the document doesn't exist, create it with the student UUID reference
            await teacherRef.set({
              AssignedUsers: [studentRef], // Set it as a reference
            });
          }
        }
      }

      return {
        message: `Student with email ${email} already exists, linked with new teachers.`,
        skipped: true,
      };
    }

    // Proceed with student creation if no existing student was found
    const studentData: any = {
      classId,
      first_name,
      last_name,
      birthdate: new Date(birthdate),
      email,
      purchase_id,
    };

    if (userId) {
      studentData.userId = userId;
    }

    const student = await this.database.student.create({
      data: studentData,
    });

    const studentTeacherRecords = teacherIds.map((teacherId) => ({
      studentId: student.id,
      teacherId: teacherId,
    }));

    await this.database.studentTeacher.createMany({
      data: studentTeacherRecords,
    });

    // Generate a unique code and order number
    const code = generateCode();
    const orderNumber = generateOrderNumber();

    // Create the purchase record
    const purchase = await this.database.purchase.create({
      data: {
        email,
        first_name,
        last_name,
        code,
        number_of_vr_glasses: 0,
        number_of_licenses: 1,
        is_subscription: false,
        duration: 84,
        order_number: orderNumber,
      },
    });
    if (!purchase_id) {
      await this.database.student.update({
        where: { id: student.id },
        data: { purchase_id: purchase.id },
      });
    }

    return { student, purchase };
  }

  async getAllStudents(
    page: number,
    take: number,
    filterSchool?: number,
    filterClass?: number,
    filterTeachers?: number[],
  ): Promise<{ students: any[]; totalCount: number }> {
    const where: any = {};

    if (filterSchool) {
      where.class = { school: { id: filterSchool } };
    }

    if (filterClass) {
      where.classId = filterClass;
    }

    if (filterTeachers && filterTeachers.length > 0) {
      where.AND = filterTeachers.map((teacherId) => ({
        studentTeachers: {
          some: {
            teacherId: teacherId,
          },
        },
      }));
    }

    try {
      const [students, totalCount] = await Promise.all([
        this.database.student.findMany({
          where,
          include: {
            user: true,
            class: {
              include: {
                school: true,
              },
            },
            studentTeachers: {
              include: {
                teacher: true,
              },
            },
            purchase: {
              select: {
                code: true,
              },
            },
          },
          skip: (page - 1) * take,
          take,
        }),
        this.database.student.count({ where }),
      ]);

      return { students, totalCount };
    } catch (error) {
      console.error('Error in getAllStudents:', error.message);
      throw new Error('Failed to fetch students');
    }
  }
  async getTeacherIdsByUUIDs(
    uuids: string[],
  ): Promise<{ uuid: string; id: number }[]> {
    try {
      const teachers = await this.database.teacher.findMany({
        where: {
          teacher_uuid: {
            in: uuids,
          },
        },
        select: {
          teacher_uuid: true,
          id: true,
        },
      });

      // Map the teachers to match the expected return type
      return teachers.map((teacher) => ({
        uuid: teacher.teacher_uuid,
        id: teacher.id,
      }));
    } catch (error) {
      console.error('Error in getTeacherIdsByUUIDs:', error.message);
      throw new Error('Failed to fetch teacher IDs');
    }
  }
  async getTeacherByUUID(teacher_uuid: string): Promise<any> {
    return this.database.teacher.findUnique({
      where: { teacher_uuid },
    });
  }

  async getStudentIdsByTeacherId(teacher_id: number): Promise<number[]> {
    const studentTeachers = await this.database.studentTeacher.findMany({
      where: { teacherId: teacher_id },
      select: { studentId: true },
    });
    return studentTeachers.map((st) => st.studentId);
  }

  async getStudentsByIds(
    studentIds: number[],
    page: number,
    take: number,
    schoolId?: number,
    classId?: number,
    filterTeachers?: number[],
  ): Promise<{ students: any[]; totalCount: number }> {
    const where: any = {
      id: { in: studentIds },
      ...(schoolId && { class: { schoolId } }),
      ...(classId && { classId }),
      ...(filterTeachers &&
        filterTeachers.length > 0 && {
          studentTeachers: {
            some: {
              teacherId: { in: filterTeachers },
            },
          },
        }),
    };

    const [students, totalCount] = await Promise.all([
      this.database.student.findMany({
        where,
        include: {
          user: true,
          class: {
            include: {
              school: true,
            },
          },
          studentTeachers: {
            include: {
              teacher: true,
            },
          },
          purchase: {
            select: {
              code: true,
            },
          },
        },
        skip: (page - 1) * take,
        take,
      }),
      this.database.student.count({ where }),
    ]);

    return { students, totalCount };
  }
  async linkStudentToTeachers(firebaseUid: string): Promise<void> {
    //  Get Firestore reference
    const db = this.firebaseService.getFirestore();

    // Fetch the user with the firebaseUid from the user table
    const user = await this.database.user.findUnique({
      where: { uuid: firebaseUid },
    });

    if (!user) {
      throw new NotFoundException(`User with UUID ${firebaseUid} not found`);
    }

    // Fetch the student using the user_id
    const student = await this.database.student.findUnique({
      where: { userId: user.id },
    });

    if (!student) {
      throw new NotFoundException(`Student with User ID ${user.id} not found`);
    }

    //Fetch the teacher IDs linked to the student in the student_teacher table
    const studentTeachers = await this.database.studentTeacher.findMany({
      where: { studentId: student.id },
    });

    const teacherIds = studentTeachers.map((st) => st.teacherId);

    //Fetch teacher UUIDs from the teacher table
    const teachers = await this.database.teacher.findMany({
      where: { id: { in: teacherIds } },
    });

    const teacherUuids = teachers.map((t) => t.teacher_uuid);

    //  Update Firestore for each teacher with the new student's Firestore reference
    for (const teacherUuid of teacherUuids) {
      const teacherDocRef = db.collection('SubAdminData').doc(teacherUuid);

      const teacherDoc = await teacherDocRef.get();
      let assignedUsers = teacherDoc.data()?.AssignedUsers || [];

      const studentRef = db.doc(`UserData/${firebaseUid}`);

      // If AssignedUsers array doesn't exist or the reference doesn't exist, add it
      if (!assignedUsers.some((ref: any) => ref.path === studentRef.path)) {
        assignedUsers.push(studentRef);

        // Update Firestore
        await teacherDocRef.update({
          AssignedUsers: assignedUsers,
        });
      }
    }
  }
  async getStudentByUUID(uuid: string) {
    try {
      const user = await this.database.user.findUnique({
        where: { uuid },
        select: { id: true }, // get the user id from the user table
      });

      if (!user) {
        throw new Error('User not found');
      }

      const student = await this.database.student.findUnique({
        where: { userId: user.id }, // match the user id from the user table
        select: { id: true }, // get the student id from the student table
      });

      if (!student) {
        throw new Error('Student not found');
      }

      return student;
    } catch (error) {
      console.error('Error fetching student by UUID:', error);
      throw new Error(error.message);
    }
  }
  async deleteUserByUUID(userUuid: string): Promise<void> {
    try {
      // get the student ID from the UUID
      const student = await this.getStudentByUUID(userUuid);
      const studentId = student.id; // Use student.id for further operations

      // get all teacher IDs associated with the student
      const teacherRelations = await this.database.studentTeacher.findMany({
        where: { studentId: studentId },
        select: { teacherId: true },
      });

      const teacherIds = teacherRelations.map((relation) => relation.teacherId);

      if (teacherIds.length === 0) {
        throw new Error('No teachers linked to this student');
      }

      // get all teacher UUIDs from the teacher table
      const teachers = await this.database.teacher.findMany({
        where: { id: { in: teacherIds } },
        select: { teacher_uuid: true },
      });

      const teacherUuids = teachers.map((teacher) => teacher.teacher_uuid);

      // remove the student reference from Firebase SubAdminData (AssignedUsers)
      await this.removeStudentFromTeachersInFirebase(teacherUuids, userUuid);

      // delete the student's document from Firebase UserData collection
      const firestore = this.firebaseService.getFirestore();
      await firestore.collection('UserData').doc(userUuid).delete();

      //delete the student from Firebase Authentication
      const auth = this.firebaseService.getAuth();
      await auth.deleteUser(userUuid);

      // delete the user from the SQL database
      await this.database.user.delete({
        where: { uuid: userUuid },
      });

      console.log(`User ${userUuid} deleted successfully.`);
    } catch (error) {
      console.error('Error deleting user:', error);
      throw new Error('Unable to delete user');
    }
  }

  private async removeStudentFromTeachersInFirebase(
    teacherUuids: string[],
    studentUuid: string,
  ) {
    const firestore = this.firebaseService.getFirestore();

    for (const teacherUuid of teacherUuids) {
      const teacherDocRef = firestore
        .collection('SubAdminData')
        .doc(teacherUuid);

      // Get the teacher document
      const teacherDoc = await teacherDocRef.get();
      let assignedUsers = teacherDoc.data()?.AssignedUsers || [];

      const studentRef = firestore.doc(`UserData/${studentUuid}`);

      // Remove the student reference if it exists
      assignedUsers = assignedUsers.filter(
        (ref: any) => ref.path !== studentRef.path,
      );

      // Update the teacher document with the modified AssignedUsers array
      await teacherDocRef.update({
        AssignedUsers: assignedUsers,
      });
    }

    console.log(`Removed student references for UUID: ${studentUuid}`);
  }
  async deleteStudentById(studentId: number): Promise<void> {
    try {
      // Find the student in the database
      const student = await this.database.student.findUnique({
        where: { id: studentId },
        select: { id: true },
      });

      if (!student) {
        throw new Error('Student not found');
      }

      // Delete the user and related records from the SQL database
      await this.database.student.delete({
        where: { id: studentId },
      });

      console.log(`Student with ID ${studentId} deleted successfully.`);
    } catch (error) {
      console.error('Error deleting student by ID:', error);
      throw new Error('Unable to delete student');
    }
  }

  async updateStudent(
    id: string,
    updateStudentDto: UpdateStudentDto,
  ): Promise<any> {
    const {
      classId,
      teacherIds,
      first_name,
      last_name,
      birthdate,
      email,
      purchase_id,
    } = updateStudentDto;

    const db = this.firebaseService.getFirestore();

    // Find the existing student by ID
    const existingStudent = await this.database.student.findUnique({
      where: { id: Number(id) },
      include: {
        studentTeachers: true, // Fetch current teacher links
        user: {
          select: { uuid: true }, // Fetch student UUID
        },
      },
    });

    if (!existingStudent) {
      throw new Error('Student not found.');
    }

    const studentUuid = existingStudent.user?.uuid;

    // Update the student's details
    const updatedStudentData: any = {
      classId,
      first_name,
      last_name,
      birthdate: new Date(birthdate),
      email,
    };

    await this.database.student.update({
      where: { id: Number(id) },
      data: updatedStudentData,
    });

    // Update student-teacher relationships
    const existingTeacherIds = existingStudent.studentTeachers.map(
      (st) => st.teacherId,
    );

    // Remove any teachers that are no longer linked
    const teachersToRemove = existingTeacherIds.filter(
      (teacherId) => !teacherIds.includes(teacherId),
    );
    const teachersToAdd = teacherIds.filter(
      (teacherId) => !existingTeacherIds.includes(teacherId),
    );

    // === Remove Old Teacher Links ===
    if (teachersToRemove.length > 0) {
      // Remove links from SQL
      await this.database.studentTeacher.deleteMany({
        where: {
          studentId: Number(id),
          teacherId: { in: teachersToRemove },
        },
      });

      // Remove links from Firestore
      for (const teacherId of teachersToRemove) {
        const teacher = await this.database.teacher.findUnique({
          where: { id: teacherId },
          select: { teacher_uuid: true },
        });

        if (teacher && teacher.teacher_uuid) {
          const teacherRef = db
            .collection('SubAdminData')
            .doc(teacher.teacher_uuid);
          const teacherDoc = await teacherRef.get();

          if (teacherDoc.exists) {
            const teacherData = teacherDoc.data();
            const assignedUsers = teacherData?.AssignedUsers || [];

            const studentRef = db.collection('UserData').doc(studentUuid);
            const updatedAssignedUsers = assignedUsers.filter(
              (assignedUser) => assignedUser.id !== studentRef.id,
            );

            await teacherRef.update({
              AssignedUsers: updatedAssignedUsers,
            });
          }
        }
      }
    }

    // === Add New Teacher Links ===
    if (teachersToAdd.length > 0) {
      const newTeacherLinks = teachersToAdd.map((teacherId) => ({
        studentId: Number(id),
        teacherId: teacherId,
      }));

      // Add new links to SQL
      await this.database.studentTeacher.createMany({
        data: newTeacherLinks,
      });

      // Add new links to Firestore
      for (const teacherId of teachersToAdd) {
        const teacher = await this.database.teacher.findUnique({
          where: { id: teacherId },
          select: { teacher_uuid: true },
        });

        if (teacher && teacher.teacher_uuid) {
          const teacherRef = db
            .collection('SubAdminData')
            .doc(teacher.teacher_uuid);
          const teacherDoc = await teacherRef.get();

          if (teacherDoc.exists) {
            const teacherData = teacherDoc.data();
            const assignedUsers = teacherData?.AssignedUsers || [];

            const studentRef = db.collection('UserData').doc(studentUuid);

            // Add the student if it's not already assigned
            if (
              !assignedUsers.some((userRef) => userRef.id === studentRef.id)
            ) {
              assignedUsers.push(studentRef);
              await teacherRef.update({
                AssignedUsers: assignedUsers,
              });
            }
          }
        }
      }
    }

    return { message: 'Student updated successfully.' };
  }

  async emailReportForTeacher(teacherUUID: string): Promise<void> {
    try {
      const teacher = await this.database.teacher.findUnique({
        where: { teacher_uuid: teacherUUID },
      });

      if (!teacher) {
        throw new Error('Teacher not found');
      }

      const report = await this.generateTeacherReport(teacher);

      if (report) {
        await this.emailTeacherReport(teacher, report, true);
      }
    } catch (error) {
      console.error('Error emailing report for teacher:', error);
      throw new Error('Unable to email report for teacher');
    }
  }

  async getTeacherReportFrequency(teacherUUID: string): Promise<number> {
    try {
      const teacher = await this.database.teacher.findUnique({
        where: { teacher_uuid: teacherUUID },
        select: { report_frequency: true },
      });

      if (!teacher) {
        throw new Error('Teacher not found');
      }

      return teacher.report_frequency;
    } catch (error) {
      console.error('Error fetching teacher report frequency:', error);
      throw new Error('Unable to fetch teacher report frequency');
    }
  }

  async updateTeacherReportFrequency(
    teacherUUID: string,
    frequency: ReportFrequency,
  ): Promise<void> {
    try {
      await this.database.teacher.update({
        where: { teacher_uuid: teacherUUID },
        data: { report_frequency: frequency },
      });
    } catch (error) {
      console.error('Error updating teacher report frequency:', error);
      throw new Error('Unable to update teacher report frequency');
    }
  }

  /**
   * Sends an email report to a specified teacher.
   *
   * @param {teacher} teacher - The teacher object containing details of the teacher.
   * @param {String} report - The report content encoded in Base64 format.
   * @returns {Promise<void>} - A promise that resolves when the email is sent.
   *
   * @throws Will log an error message if the email sending fails.
   *
   * @example
   * const teacher = {
   *   teacher_uuid: '12345',
   *   first_name: 'John',
   *   last_name: 'Doe'
   * };
   * const report = 'base64EncodedReportContent';
   * await emailTeacherReport(teacher, report);
   */
  async emailTeacherReport(
    teacher: teacher,
    report: String,
    sendToSupport: Boolean,
  ): Promise<void> {
    try {
      let db = this.firebaseService.getFirestore();

      // get teacher info
      let teacherUUID = teacher.teacher_uuid;
      let teacherInfo = await db
        .collection('SubAdminData')
        .doc(teacherUUID)
        .get();
      let teacherData = teacherInfo.data();

      let teacherEmail = teacherData.Email;
      let displayName = teacher.first_name;

      console.log(
        `Sending report for teacher ${teacher.first_name} ${teacher.last_name} (${teacherEmail})`,
      );

      let currentDate = moment().format('Do MMMM YYYY');
      let subject = `IMVI report for ${currentDate}`;

      let body = `Dear ${displayName},\n\nPlease find attached the report containing the training statistics until ${currentDate}.\n\nBest regards,\nIMVI`;

      const mailjet = this.mailService.getMailJet();

      await mailjet.post('send', { version: 'v3.1' }).request({
        Messages: [
          {
            From: {
              Email: '<EMAIL>',
              Name: 'Training report from IMVI',
            },
            To: [
              {
                Email: sendToSupport ? '<EMAIL>' : teacherEmail,
                Name: displayName,
              },
            ],
            Bcc: [
              {
                Email: '<EMAIL>',
                Name: 'IMVI Support',
              },
            ],
            Subject: subject,
            TextPart: body,
            Attachments: [
              {
                ContentType: 'application/pdf',
                Filename: `report_${moment().format('DD-MMMM-YYYY')}.pdf`,
                Base64Content: report,
              },
            ],
          },
        ],
      });
    } catch (error) {
      console.log(
        `Failed sending report for teacher ${teacher.first_name} ${teacher.last_name} (${teacher.teacher_uuid}) with error: `,
        error,
      );
    }
  }

  async generateDailyReports(): Promise<void> {
    try {
      // get all teachers
      const teachers = await this.database.teacher.findMany();

      // for each teacher fetch students
      for (const teacher of teachers) {
        //if (teacher.id == 16) {
        console.log('---------------------------------');
        console.log(
          `Process info for teacher: ${teacher.first_name} ${teacher.last_name}`,
        );

        let reportFrequency = teacher.report_frequency || ReportFrequency.None;

        console.log(`Teacher has report frequency set to : ${reportFrequency}`);

        let sendReport = false;

        switch (reportFrequency) {
          case ReportFrequency.Daily:
            sendReport = true;
            console.log(
              'Teacher has daily report frequency > We build and send the report',
            );
            break;
          case ReportFrequency.Weekly:
            // check if it's Monday
            if (moment().isoWeekday() == 1) {
              sendReport = true;
              console.log(
                "Teacher has weekly report frequency & it's Monday > We build and send the report",
              );
            } else {
              console.log(
                "Teacher has weekly report frequency but it's not Monday > Not sending report",
              );
            }
            break;
          case ReportFrequency.Monthly:
            // check if it's the first day of the month
            let firstWorkingDay = moment().startOf('month').day();
            if (firstWorkingDay === 0) {
              firstWorkingDay += 1; // If the first day is Sunday, move to Monday
            } else if (firstWorkingDay === 6) {
              firstWorkingDay += 2; // If the first day is Saturday, move to Monday
            }
            if (moment().date() === firstWorkingDay) {
              sendReport = true;
              console.log(
                "Teacher has monthly report frequency & it's first working day of the month > We build and send the report",
              );
            } else {
              console.log(
                "Teacher has monthly report frequency but it's not the first working day of the month > Not sending report",
              );
            }
            break;
          default:
            console.log(
              'No report frequency set for teacher > Not sending report',
            );
            break;
        }

        if (sendReport) {
          console.log(
            `Generating report for teacher ${teacher.first_name} ${teacher.last_name} ...`,
          );
          let report = await this.generateTeacherReport(teacher);

          if (report) {
            console.log(
              `Email report for teacher ${teacher.first_name} ${teacher.last_name} ...`,
            );
            await this.emailTeacherReport(teacher, report, false);
          } else {
            console.log(
              `No report generated for teacher ${teacher.first_name} ${teacher.last_name} ...`,
            );
          }
        }
        //}
      }
    } catch (error) {
      console.log('Failed generating reports with error: ', error);
    }
  }

  async generateReadingData(
    student: Student,
    trainingStartDate,
  ): Promise<ReadingData> {
    try {
      let readingData: ReadingData = {
        headers: [],
        lettersResults: [],
        wordsResults: [],
        sentencesResults: [],
        improvementsHeaders: [],
        wordsImprovements: [],
        lettersImprovements: [],
        sentencesImprovements: [],
        datasetKeys: [],
        lettersDataset: [],
        wordsDataset: [],
        sentencesDataset: [],
      };

      // fetch reading tests results
      const studentEmail = student.user.additional_info.email;
      const readingTestResultsUrl =
        'https://europe-west3-app-owlreading.cloudfunctions.net/getUserReadingTestsResults';
      let response = await lastValueFrom(
        this.httpService.post(readingTestResultsUrl, {
          data: {
            email: studentEmail,
          },
        }),
      );

      //console.log('Reading Test Results: ', response.data.data);
      let readingResults = response.data.data;
      readingResults.sort((resultA, resultB) => {
        return moment(resultA.date).diff(moment(resultB.date));
      });

      readingData.headers.push({ content: 'Date', width: 60 });
      readingData.improvementsHeaders = [];

      readingData.lettersResults.push({ content: 'Letters', width: 60 });
      readingData.lettersImprovements = [];
      readingData.lettersDataset.push(null);

      readingData.wordsResults.push({ content: 'Words', width: 60 });
      readingData.wordsImprovements = [];
      readingData.wordsDataset.push(null);

      readingData.sentencesResults.push({ content: 'Sentences', width: 60 });
      readingData.sentencesImprovements = [];
      readingData.sentencesDataset = [null];

      let previousReadingResult = null;

      for (const readingResult of readingResults) {
        let weeksDifference = Math.abs(
          trainingStartDate.diff(readingResult.date, 'weeks'),
        );

        readingData.headers.push({
          content: `w${weeksDifference}`,
          width: 50,
        });

        readingData.lettersResults.push({
          content: readingResult.letters_score || '-',
          width: 50,
        });

        readingData.wordsResults.push({
          content: readingResult.words_score || '-',
          width: 50,
        });

        readingData.sentencesResults.push({
          content: readingResult.sentences_score || '-',
          width: 50,
        });

        if (previousReadingResult) {
          readingData.improvementsHeaders.push({
            content: `w${weeksDifference}`,
            width: 50,
          });

          // calculate letters improvements
          if (previousReadingResult.letters_score != 0) {
            let improvement =
              ((readingResult.letters_score -
                previousReadingResult.letters_score) /
                previousReadingResult.letters_score) *
              100;

            readingData.lettersImprovements.push({
              content: `${improvement.toFixed(1)}%`,
              width: 50,
            });
            readingData.lettersDataset.push(improvement);
          } else {
            readingData.lettersImprovements.push({ content: '-', width: 50 });
            readingData.lettersDataset.push(0);
          }

          // calculate words improvements
          if (previousReadingResult.words_score != 0) {
            let improvement =
              ((readingResult.words_score - previousReadingResult.words_score) /
                previousReadingResult.words_score) *
              100;

            readingData.wordsImprovements.push({
              content: `${improvement.toFixed(1)}%`,
              width: 50,
            });
            readingData.wordsDataset.push(improvement);
          } else {
            readingData.wordsImprovements.push({ content: '-', width: 50 });
            readingData.wordsDataset.push(0);
          }

          // calculate sentences improvements
          if (previousReadingResult.sentences_score != 0) {
            let improvement =
              ((readingResult.sentences_score -
                previousReadingResult.sentences_score) /
                previousReadingResult.sentences_score) *
              100;

            readingData.sentencesImprovements.push({
              content: `${improvement.toFixed(1)}%`,
              width: 50,
            });
            readingData.sentencesDataset.push(improvement);
          } else {
            readingData.sentencesImprovements.push({ content: '-', width: 50 });
            readingData.sentencesDataset.push(0);
          }
        } else {
          readingData.lettersDataset.push(0);
          readingData.wordsDataset.push(0);
          readingData.sentencesDataset.push(0);
        }

        previousReadingResult = readingResult;
      }

      readingData.lettersDataset.push(null);
      readingData.wordsDataset.push(null);
      readingData.sentencesDataset.push(null);

      return readingData;
    } catch (error) {
      console.error('Error generating reading data:', error);
    }
  }
  async generateVergenceData(
    student: Student,
    trainingStartDate,
  ): Promise<VergenceData> {
    try {
      let vergenceData: VergenceData = {
        headers: [],
        results: [],
        improvementsHeaders: [],
        improvements: [],
        datasetKeys: [],
        dataset: [],
      };

      let vergenceResults = await this.vergenceService.getVergenceResults(
        student.user.uuid,
      );

      let sessions = vergenceResults.sessions;
      let finalSessions = {};
      // keep only the last sessions from each day
      sessions.forEach((session) => {
        // get diffrence in weeks between start of training and date when test was taken
        let weeksDifference = Math.abs(
          trainingStartDate.diff(session.created_at, 'weeks'),
        );
        if (
          !finalSessions[weeksDifference] ||
          (finalSessions[weeksDifference] &&
            moment(session.created_at).isAfter(
              moment(finalSessions[weeksDifference].created_at),
            ))
        ) {
          finalSessions[weeksDifference] = session;
        }
      });

      vergenceData.headers.push({ content: 'Date', width: 60 });
      vergenceData.results.push({
        content: 'Vergence',
        width: 60,
      });

      vergenceData.improvementsHeaders = [];
      vergenceData.improvements = [];
      vergenceData.datasetKeys.push('');
      vergenceData.dataset.push(null);

      let previousScore = null;

      for (let key in finalSessions) {
        let session = finalSessions[key];
        let score = 0;
        let targetScore = 0;
        session.questions.forEach((question) => {
          score += question.value;
          targetScore += question.maxScore;
        });
        vergenceData.headers.push({ content: `w${key}`, width: 50 });
        vergenceData.datasetKeys.push(`w${key}`);
        vergenceData.results.push({ content: `${score}`, width: 50 });

        // calculate improvement since last test
        if (previousScore) {
          vergenceData.improvementsHeaders.push({
            content: `w${key}`,
            width: 50,
          });
          if (previousScore != 0) {
            let improvement = ((score - previousScore) / previousScore) * 100;
            vergenceData.improvements.push({
              content: `${improvement.toFixed(1)}%`,
              width: 50,
            });
            vergenceData.dataset.push(improvement);
          } else {
            vergenceData.improvements.push({ content: '-', width: 50 });
            vergenceData.dataset.push(0);
          }
        } else {
          vergenceData.dataset.push(0);
        }
        previousScore = score;
      }

      vergenceData.datasetKeys.push('');
      vergenceData.dataset.push(null);

      return vergenceData;
    } catch (error) {
      console.error('Error generating vergence data:', error);
    }
  }

  async drawReadingResultsTable(
    pdfDocument: PDFDocument,
    readingData: ReadingData,
    coordinate: Coordinates,
  ): Promise<void> {
    // display reading tests results
    pdfDocument
      .font('Helvetica-Bold')
      .fontSize(10)
      .text('Reading test', coordinate.x, coordinate.y, { align: 'left' });
    pdfDocument.moveDown();
    pdfDocument.font('Helvetica');
    coordinate.y += rowHeight;

    let testResultsHeaderWidth = readingData.headers.reduce(
      (totalWidth, currentColumn) => {
        return totalWidth + currentColumn.width;
      },
      0,
    );

    this.drawCell(
      pdfDocument,
      coordinate.x,
      coordinate.y,
      testResultsHeaderWidth,
      rowHeight,
      'Test Results',
    );

    coordinate.x += testResultsHeaderWidth;

    if (readingData.improvementsHeaders.length > 0) {
      let improvementsHeaderWidth = Math.max(
        150,
        readingData.improvementsHeaders.reduce((totalWidth, currentColumn) => {
          return totalWidth + currentColumn.width;
        }, 0),
      );

      this.drawCell(
        pdfDocument,
        coordinate.x,
        coordinate.y,
        improvementsHeaderWidth,
        rowHeight,
        'Improvements',
      );
    }

    coordinate.x = 50;
    coordinate.y += rowHeight;

    for (const header of readingData.headers) {
      this.drawCell(
        pdfDocument,
        coordinate.x,
        coordinate.y,
        header.width,
        rowHeight,
        header.content,
      );

      coordinate.x += header.width;
    }

    if (readingData.improvementsHeaders.length > 0) {
      for (const improvementHeader of readingData.improvementsHeaders) {
        this.drawCell(
          pdfDocument,
          coordinate.x,
          coordinate.y,
          improvementHeader.width,
          rowHeight,
          improvementHeader.content,
        );

        coordinate.x += improvementHeader.width;
      }
    }

    coordinate.x = 50;
    coordinate.y += rowHeight;

    for (const lettersResult of readingData.lettersResults) {
      this.drawCell(
        pdfDocument,
        coordinate.x,
        coordinate.y,
        lettersResult.width,
        rowHeight,
        lettersResult.content,
      );

      coordinate.x += lettersResult.width;
    }

    for (const lettersImprovement of readingData.lettersImprovements) {
      this.drawCell(
        pdfDocument,
        coordinate.x,
        coordinate.y,
        lettersImprovement.width,
        rowHeight,
        lettersImprovement.content,
      );

      coordinate.x += lettersImprovement.width;
    }

    coordinate.x = 50;
    coordinate.y += rowHeight;

    for (const wordsResult of readingData.wordsResults) {
      this.drawCell(
        pdfDocument,
        coordinate.x,
        coordinate.y,
        wordsResult.width,
        rowHeight,
        wordsResult.content,
      );

      coordinate.x += wordsResult.width;
    }

    for (const wordsImprovement of readingData.wordsImprovements) {
      this.drawCell(
        pdfDocument,
        coordinate.x,
        coordinate.y,
        wordsImprovement.width,
        rowHeight,
        wordsImprovement.content,
      );

      coordinate.x += wordsImprovement.width;
    }

    coordinate.x = 50;
    coordinate.y += rowHeight;

    for (const sentencesResult of readingData.sentencesResults) {
      this.drawCell(
        pdfDocument,
        coordinate.x,
        coordinate.y,
        sentencesResult.width,
        rowHeight,
        sentencesResult.content,
      );

      coordinate.x += sentencesResult.width;
    }

    for (const sentencesImprovement of readingData.sentencesImprovements) {
      this.drawCell(
        pdfDocument,
        coordinate.x,
        coordinate.y,
        sentencesImprovement.width,
        rowHeight,
        sentencesImprovement.content,
      );

      coordinate.x += sentencesImprovement.width;
    }

    coordinate.x = 50;
    coordinate.y += 2 * rowHeight;
  }

  async drawVergenceResultsTable(
    pdfDocument: PDFDocument,
    vergenceData: VergenceData,
    coordinate: Coordinates,
  ): Promise<void> {
    // draw title
    pdfDocument
      .fontSize(10)
      .text('Vergence test', coordinate.x, coordinate.y, { align: 'left' });
    pdfDocument.moveDown();
    pdfDocument.font('Helvetica');
    coordinate.y += rowHeight;

    let testResultsHeaderWidth = vergenceData.headers.reduce(
      (totalWidth, currentColumn) => {
        return totalWidth + currentColumn.width;
      },
      0,
    );

    this.drawCell(
      pdfDocument,
      coordinate.x,
      coordinate.y,
      testResultsHeaderWidth,
      rowHeight,
      'Test Results',
    );

    coordinate.x += testResultsHeaderWidth;

    if (vergenceData.improvementsHeaders.length > 0) {
      let improvementsHeaderWidth = Math.max(
        150,
        vergenceData.improvementsHeaders.reduce((totalWidth, currentColumn) => {
          return totalWidth + currentColumn.width;
        }, 0),
      );

      this.drawCell(
        pdfDocument,
        coordinate.x,
        coordinate.y,
        improvementsHeaderWidth,
        rowHeight,
        'Improvements',
      );
    }

    coordinate.x = 50;
    coordinate.y += rowHeight;

    for (const header of vergenceData.headers) {
      this.drawCell(
        pdfDocument,
        coordinate.x,
        coordinate.y,
        header.width,
        rowHeight,
        header.content,
      );

      coordinate.x += header.width;
    }

    if (vergenceData.improvementsHeaders.length > 0) {
      for (const improvementHeader of vergenceData.improvementsHeaders) {
        this.drawCell(
          pdfDocument,
          coordinate.x,
          coordinate.y,
          improvementHeader.width,
          rowHeight,
          improvementHeader.content,
        );

        coordinate.x += improvementHeader.width;
      }
    }

    coordinate.x = 50;
    coordinate.y += rowHeight;

    for (const vergence of vergenceData.results) {
      this.drawCell(
        pdfDocument,
        coordinate.x,
        coordinate.y,
        vergence.width,
        rowHeight,
        vergence.content,
      );

      coordinate.x += vergence.width;
    }

    if (vergenceData.improvements.length > 0) {
      for (const improvement of vergenceData.improvements) {
        this.drawCell(
          pdfDocument,
          coordinate.x,
          coordinate.y,
          improvement.width,
          rowHeight,
          improvement.content,
        );

        coordinate.x += improvement.width;
      }
    }
  }

  async drawChart(
    pdfDocument: PDFDocument,
    vergenceData: VergenceData,
    readingData: ReadingData,
    coordinate: Coordinates,
  ): Promise<void> {
    // draw graph
    const width = 600; //px
    const height = 250; //px
    const backgroundColour = 'white';

    const chartJSNodeCanvas = new ChartJSNodeCanvas({
      width,
      height,
      backgroundColour,
    });

    const datasets = [
      {
        label: 'Vergence Test',
        backgroundColor: 'rgba(201, 0, 250, 1.0)',
        borderColor: 'rgba(201, 0, 250, 1.0)',
        borderWidth: 0.5,
        data: vergenceData.dataset,
      },
      {
        label: 'Letters Test',
        backgroundColor: 'rgba(251, 0, 67, 0.9)',
        borderColor: 'rgba(251, 0, 67, 1.0)',
        borderWidth: 0.5,
        data: readingData.lettersDataset,
      },
      {
        label: 'Words Test',
        backgroundColor: 'rgba(31, 103, 143, 1.0)',
        borderColor: 'rgba(31, 103, 143, 1.0)',
        borderWidth: 0.5,
        data: readingData.wordsDataset,
      },
      {
        label: 'Sentences Test',
        backgroundColor: 'rgba(253, 166, 8, 0.9)',
        borderColor: 'rgba(253, 166, 8, 1.0)',
        borderWidth: 0.5,
        data: readingData.sentencesDataset,
      },
    ];

    const configuration: ChartConfiguration<ValidChartType> = {
      type: 'line',
      data: {
        labels: vergenceData.datasetKeys,
        datasets,
      },
      options: {
        scales: {
          y: {
            beginAtZero: true,
          },
        },
        plugins: {
          legend: {
            display: true,
            labels: {
              usePointStyle: true,
              pointStyle: 'circle',
              boxWidth: 10,
              boxHeight: 10,
            },
          },
        },
      },
    };

    const chartBuffer = chartJSNodeCanvas.renderToBufferSync(
      configuration,
      'image/png',
    );

    if (chartBuffer) {
      pdfDocument.moveDown();
      await pdfDocument.image(chartBuffer, coordinate.x, coordinate.y, {
        fit: [600, 250],
        align: 'center',
      });
    } else {
      console.log('XXXXX --- BUFFER INVALID ---- XXXXXXX');
    }
  }

  async drawHeatMapCell(
    pdfDocument: PDFDocument,
    x: number,
    y: number,
    color: string,
  ): Promise<void> {
    pdfDocument.rect(x + 0.5, y + 0.5, 9, 9).fill(color);
  }

  async generateTeacherReport(teacher: teacher): Promise<String> {
    try {
      let db = this.firebaseService.getFirestore();

      // fetch students ids for this teacher
      const studentsIds = await this.getStudentIdsByTeacherId(teacher.id);

      if (studentsIds.length > 0) {
        // get students with these ids
        let students = await this.database.student.findMany({
          where: {
            id: {
              in: studentsIds,
            },
          },
          include: {
            user: {
              include: {
                additional_info: true,
              },
            },
          },
        });

        students = students.filter(
          (student) => student.user && student.user.uuid,
        );

        // local file info
        let currentDate = moment().format('YYYY-MM-DD').toString();

        // create report file
        const reportDocument = new PDFDocument({ layout: 'landscape' });

        let x = 10;
        let y = 50;

        //add teacher name
        reportDocument
          .font('Helvetica-Bold')
          .fontSize(20)
          .text(`Teacher: ${teacher.first_name} ${teacher.last_name}`, x, y, {
            align: 'left',
          });

        y += 40;
        x += 155;

        // draw heatmap color legend
        reportDocument.circle(x, y, 5).fill('green');
        reportDocument
          .fillColor('black')
          .font('Helvetica')
          .fontSize(10)
          .text('Trained', x + 10, y - 2.5, {
            align: 'left',
          });

        x += 100;

        reportDocument.circle(x, y, 5).fill('yellow');
        reportDocument
          .fillColor('black')
          .font('Helvetica')
          .fontSize(10)
          .text('None 2 days', x + 10, y - 2.5, {
            align: 'left',
          });

        x += 100;

        reportDocument.circle(x, y, 5).fill('orange');
        reportDocument
          .fillColor('black')
          .font('Helvetica')
          .fontSize(10)
          .text('None 3 days', x + 10, y - 2.5, {
            align: 'left',
          });

        x += 100;
        reportDocument.circle(x, y, 5).fill('red');
        reportDocument
          .fillColor('black')
          .font('Helvetica')
          .fontSize(10)
          .text('None > 3 days', x + 10, y - 2.5, {
            align: 'left',
          });

        x += 100;
        reportDocument.circle(x, y, 5).fill('#d3d3d3');
        reportDocument
          .fillColor('black')
          .font('Helvetica')
          .fontSize(10)
          .text('No training', x + 10, y - 2.5, {
            align: 'left',
          });

        x = 10;
        y += 10;

        // generate data for heat map
        let generalStartDate = moment();
        let heatMapData = [];

        for (const student of students) {
          console.log(
            `Process data for student: ${student.first_name} ${student.last_name}`,
          );

          // get student training data
          let studentInfo = await db
            .collection('UserData')
            .doc(student.user.uuid)
            .get();

          let studentData = studentInfo.data();

          // we only take into accounts students that have started training
          if (studentData && studentData.TrainingStartedOn) {
            let trainingStartDate = moment(
              studentData.TrainingStartedOn.toDate(),
            );

            // check if student training started before general start date
            if (trainingStartDate.isBefore(generalStartDate)) {
              generalStartDate = trainingStartDate;
            }

            // get program data
            let program = await studentData.Programs.get();
            let programData = program.data();
            let sessions = programData.SessionsDoneLog;

            if (sessions.length > 0) {
              sessions = sessions.map((session) =>
                moment(session.SessionDoneAt.toDate()).format('YYYY-MM-DD'),
              );

              //console.log('Sessions: ', sessions);
              heatMapData.push({
                name: `${student.first_name} ${student.last_name}`,
                data: sessions,
              });
            }
          }
        }

        console.log('Building heatmap....');
        // build heat map
        let pageIndex = 0;
        for (const heatMap of heatMapData) {
          pageIndex++;
          let studentName = heatMap.name;
          // draw name
          reportDocument
            .fillColor('black')
            .font('Helvetica')
            .fontSize(10)
            .text(studentName, x, y + 2, {
              align: 'left',
            });

          reportDocument.link(x, y, 100, 10, { page: pageIndex });

          x += 150;

          let startDate = moment(generalStartDate).startOf('day');
          let currentDate = moment().add(1, 'day').startOf('day');

          while (startDate.isSameOrBefore(currentDate)) {
            let day = startDate.format('YYYY-MM-DD');

            let color = '#d3d3d3';

            if (startDate.isSame(currentDate)) {
              color = 'gray';
            } else {
              if (heatMap.data.includes(day)) {
                color = 'green';
              } else {
                //check if the user trained the previous day
                let yesterday = moment(startDate)
                  .subtract(1, 'day')
                  .format('YYYY-MM-DD');
                if (!heatMap.data.includes(yesterday)) {
                  color = 'yellow';
                  //check if the user trained the day before yesterday
                  let dayBeforeYesterday = moment(yesterday)
                    .subtract(1, 'day')
                    .format('YYYY-MM-DD');
                  if (!heatMap.data.includes(dayBeforeYesterday)) {
                    color = 'orange';
                    //check if the user trained 3 days ago
                    let threeDaysAgo = moment(dayBeforeYesterday)
                      .subtract(1, 'day')
                      .format('YYYY-MM-DD');
                    if (!heatMap.data.includes(threeDaysAgo)) {
                      color = 'red';
                    }
                  }
                }
              }
            }

            this.drawHeatMapCell(reportDocument, x, y, color);
            startDate.add(1, 'day');

            x += 10;
          }
          x = 10;
          y += 10;
        }

        reportDocument.addPage();

        // generate reports for each student
        for (const student of students) {
          // only build reports for students that have created an account
          if (student.user && student.user.uuid) {
            let studentUUID = student.user.uuid;

            let studentInfo = await db
              .collection('UserData')
              .doc(studentUUID)
              .get();

            let studentData = studentInfo.data();

            // we only take into accounts students that have started training
            if (studentData && studentData.TrainingStartedOn) {
              let trainingStartDate = moment(
                studentData.TrainingStartedOn.toDate(),
              );

              // add student name
              reportDocument
                .font('Helvetica-Bold')
                .fontSize(15)
                .text(`${student.first_name} ${student.last_name}`, 50, 50, {
                  align: 'left',
                });
              reportDocument.moveDown();

              let coordinate: Coordinates = {
                x: 50,
                y: 100,
              };

              // fetch vergence data
              console.log(
                `Generating vergence data for student ${student.first_name} ${student.last_name} ...`,
              );
              let vergenceData = await this.generateVergenceData(
                student,
                moment().startOf('year'), //trainingStartDate,
              );

              // build vergence results table
              console.log(
                `Drawing vergence results table for student ${student.first_name} ${student.last_name} ...`,
              );
              await this.drawVergenceResultsTable(
                reportDocument,
                vergenceData,
                coordinate,
              );

              coordinate.x = 50;
              coordinate.y += 2 * rowHeight;

              console.log(
                `Generating reading data for student ${student.first_name} ${student.last_name} ...`,
              );
              let readingData = await this.generateReadingData(
                student,
                moment().startOf('year'), //trainingStartDate,
              );

              console.log(
                `Drawing reading results table for student ${student.first_name} ${student.last_name} ...`,
              );
              await this.drawReadingResultsTable(
                reportDocument,
                readingData,
                coordinate,
              );

              coordinate.x = 50;
              coordinate.y += rowHeight;

              console.log(
                `Drawing chart for student ${student.first_name} ${student.last_name} ...`,
              );
              await this.drawChart(
                reportDocument,
                vergenceData,
                readingData,
                coordinate,
              );

              reportDocument.addPage();
            }
          }
        }

        reportDocument.end();
        console.log(
          `Report for teacher ${teacher.first_name} ${teacher.last_name} generated successfully.`,
        );

        // We will write to a buffer using a writable stream
        const chunks: Buffer[] = [];
        const writable = new Writable({
          write(chunk, encoding, callback) {
            chunks.push(chunk);
            callback();
          },
        });

        await pipeline(reportDocument, writable);

        const pdfBuffer = Buffer.concat(chunks);
        const base64String = pdfBuffer.toString('base64');
        console.log(`Transforming report to base64 ...`);

        return base64String;
      } else {
        console.log(
          `Could not find any students for teacher: ${teacher.first_name} ${teacher.last_name} (ID: ${teacher.id})`,
        );
        return null;
      }
    } catch (error) {
      console.log(
        `Failed to generate reports for teacher ${teacher.first_name} ${teacher.last_name} (ID: ${teacher.id}) with error: ${error}`,
      );
    }
  }

  drawCell(
    doc: PDFDocument,
    x: number,
    y: number,
    width: number,
    height: number,
    text: string,
  ) {
    doc.rect(x, y, width, height).stroke(); // Draw cell border
    doc.text(text, x + 5, y + 5, {
      width: width - 10,
      align: 'center',
    }); // Add text inside the cell
  }
  async createSchool(data: CreateSchoolDto) {
    return await this.database.school.create({
      data: {
        name: data.schoolName,
        city: data.city,
      },
    });
  }

  async createClass(data: CreateClassDto) {
    return await this.database.schoolClass.create({
      data: {
        name: data.className,
        schoolId: data.schoolId,
      },
    });
  }
}
