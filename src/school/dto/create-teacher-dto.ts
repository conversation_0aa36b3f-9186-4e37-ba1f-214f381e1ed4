import { IsNotEmpty, <PERSON>S<PERSON>, IsEmail } from 'class-validator';

export class CreateTeacherDto {
  @IsNotEmpty()
  @IsString()
  first_name: string;

  @IsNotEmpty()
  @IsString()
  last_name: string;

  @IsNotEmpty()
  @IsString()
  phone_number: string;

  @IsNotEmpty()
  @IsEmail()
  @IsString()
  email: string;

  @IsNotEmpty()
  @IsString()
  role: string;

  @IsNotEmpty()
  schoolId: number;

  @IsNotEmpty()
  classId: number;
}
