import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';
import { SchoolClassDto } from './school-class-dto';

export class SchoolDto {
  @ApiProperty({ description: 'ID of the school' })
  @IsNotEmpty()
  readonly id: number;

  @ApiProperty({ description: 'Name of the school' })
  @IsString()
  @IsNotEmpty()
  readonly name: string;

  @ApiProperty({ description: 'City where the school is located' })
  @IsString()
  @IsNotEmpty()
  readonly city: string;

  @ApiProperty({ description: 'Classes of the school', type: [SchoolClassDto] })
  readonly classes: SchoolClassDto[];
}
