import {
  IsNotEmpty,
  IsArray,
  IsInt,
  IsOptional,
  IsString,
  IsDateString,
} from 'class-validator';

export class CreateStudentDto {
  @IsOptional()
  @IsInt()
  userId?: number;

  @IsNotEmpty()
  @IsInt()
  classId: number;

  @IsArray()
  teacherIds: number[];

  @IsNotEmpty()
  @IsString()
  first_name: string;

  @IsNotEmpty()
  @IsString()
  last_name: string;

  @IsNotEmpty()
  @IsDateString()
  birthdate: string;

  @IsNotEmpty()
  @IsString()
  email: string;

  @IsOptional()
  @IsInt()
  purchase_id?: number;
}
