import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class SchoolClassDto {
  @ApiProperty({ description: 'ID of the class' })
  @IsNotEmpty()
  @IsNumber()
  readonly id: number;

  @ApiProperty({ description: 'Name of the class' })
  @IsString()
  @IsNotEmpty()
  readonly name: string;

  @ApiProperty({ description: 'ID of the school the class belongs to' })
  @IsNotEmpty()
  @IsNumber()
  readonly schoolId: number;
}
