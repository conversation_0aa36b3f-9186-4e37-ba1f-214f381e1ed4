import {
  IsOptional,
  <PERSON><PERSON><PERSON>y,
  IsInt,
  IsString,
  IsDateString,
} from 'class-validator';

export class UpdateStudentDto {
  @IsOptional()
  @IsInt()
  userId?: number;

  @IsOptional()
  @IsInt()
  classId?: number;

  @IsOptional()
  @IsArray()
  teacherIds?: number[];

  @IsOptional()
  @IsString()
  first_name?: string;

  @IsOptional()
  @IsString()
  last_name?: string;

  @IsDateString()
  birthdate?: string;

  @IsOptional()
  @IsString()
  email?: string;

  @IsOptional()
  @IsInt()
  purchase_id?: number;
}
