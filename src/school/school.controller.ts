import {
  Controller,
  Get,
  Param,
  Post,
  Body,
  HttpStatus,
  Res,
  Query,
  HttpException,
  Delete,
  Put,
} from '@nestjs/common';
import { ApiOperation, ApiTags, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';
import { SchoolService } from './school.service';
import { SchoolDto } from './dto/school-dto';
import { SchoolClassDto } from './dto/school-class-dto';
import { CreateStudentDto } from './dto/create-student-dto';
import { CreateTeacherDto } from './dto/create-teacher-dto';
import { LinkUserToTeachersDto } from './dto/link-student-teacher-dto';
import { UpdateStudentDto } from './dto/update-student-dto';
import { updateTeacherReportFrequencyDto } from './dto/update-teacher-report-frequency-dto';
import { CreateClassDto } from './dto/create-class-dto';
import { CreateSchoolDto } from './dto/create-school-dto';

@ApiTags('school')
@Controller('school')
export class SchoolController {
  constructor(private readonly schoolService: SchoolService) {}

  @Get('/')
  @ApiOperation({
    summary: 'Get all schools',
    description: 'Fetches all schools from the database.',
  })
  @ApiResponse({
    status: 200,
    description: 'List of schools with classes and teachers',
    type: [SchoolDto],
  })
  async getAllSchools(@Res() res: Response) {
    const result = await this.schoolService.getAllSchools();
    return res.status(HttpStatus.OK).json(result);
  }

  @Get('/generate-daily-reports')
  @ApiOperation({
    summary: 'Generate daily reports for all students',
    description: 'Build PDF reports for all students',
  })
  @ApiResponse({
    status: 200,
    description: 'Reports were generated',
  })
  async generateDailyReports(@Res() res: any) {
    await this.schoolService.generateDailyReports();
    return res.status(HttpStatus.OK).json({ status: 'Reports were generated' });
  }

  @Get('/:id/classes')
  @ApiOperation({
    summary: 'Get classes by school ID',
    description: 'Fetches all classes for a given school ID from the database.',
  })
  @ApiResponse({
    status: 200,
    description: 'List of classes',
    type: [SchoolClassDto],
  })
  async getClassesBySchoolId(
    @Param('id') schoolId: number,
    @Res() res: Response,
  ) {
    const result = await this.schoolService.getClassesBySchoolId(schoolId);
    return res.status(HttpStatus.OK).json(result);
  }

  @Post('/teacher')
  @ApiOperation({
    summary: 'Create a new teacher',
    description: 'Creates a new teacher with the provided details.',
  })
  @ApiResponse({
    status: 201,
    description: 'The teacher has been successfully created.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request.',
  })
  async createTeacher(
    @Body() createTeacherDto: CreateTeacherDto,
    @Res() res: Response,
  ) {
    try {
      const teacher = await this.schoolService.createTeacher(createTeacherDto);
      return res.status(HttpStatus.CREATED).json(teacher);
    } catch (error) {
      return res
        .status(HttpStatus.BAD_REQUEST)
        .json({ message: error.message });
    }
  }

  @Post('/student')
  @ApiOperation({
    summary: 'Create a new student',
    description: 'Creates a new student with the provided details.',
  })
  @ApiResponse({
    status: 201,
    description: 'The student has been successfully created.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request.',
  })
  async createStudent(
    @Body() createStudentDto: CreateStudentDto,
    @Res() res: Response,
  ) {
    try {
      const student = await this.schoolService.createStudent(createStudentDto);
      return res.status(HttpStatus.CREATED).json(student);
    } catch (error) {
      return res
        .status(HttpStatus.BAD_REQUEST)
        .json({ message: error.message });
    }
  }

  @Put('/student/:id')
  @ApiOperation({
    summary: 'Update an existing student',
    description: 'Updates a student with the provided details.',
  })
  @ApiResponse({
    status: 200,
    description: 'The student has been successfully updated.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request.',
  })
  async updateStudent(
    @Param('id') id: string,
    @Body() updateStudentDto: UpdateStudentDto,
    @Res() res: Response,
  ) {
    try {
      const updatedStudent = await this.schoolService.updateStudent(
        id,
        updateStudentDto,
      );
      return res.status(HttpStatus.OK).json(updatedStudent);
    } catch (error) {
      return res
        .status(HttpStatus.BAD_REQUEST)
        .json({ message: error.message });
    }
  }

  @Get('/students')
  @ApiOperation({
    summary:
      'Get all students associated with a teacher with pagination and filtering',
    description:
      'Fetches all students associated with a teacher from the database with pagination and optional filtering by school and class.',
  })
  @ApiResponse({
    status: 200,
    description: 'List of students with total count',
  })
  @Get('/students')
  @ApiOperation({
    summary: 'Get all students with pagination and filtering',
    description:
      'Fetches all students with pagination and optional filtering by school, class, and teacher UUID.',
  })
  @ApiResponse({
    status: 200,
    description: 'List of students with total count',
  })
  async getAllStudents(
    @Res() res: Response,
    @Query('page') page: string,
    @Query('take') take: string,
    @Query('schoolId') schoolId?: string,
    @Query('classId') classId?: string,
    @Query('teacherIds') teacherIds?: string,
    @Query('teacher_uuid') teacher_uuid?: string,
    @Query('isAdmin') isAdmin?: string,
  ) {
    const pageInt = parseInt(page, 10);
    const takeInt = parseInt(take, 10);
    const schoolIdInt = schoolId ? parseInt(schoolId, 10) : undefined;
    const classIdInt = classId ? parseInt(classId, 10) : undefined;

    // Parse teacherIds query parameter, handle empty or undefined
    const filterTeachers = teacherIds
      ? teacherIds.split(',').map((id) => parseInt(id, 10))
      : [];

    try {
      if (isAdmin === 'true') {
        // Admin users should see all students with applied filters
        const result = await this.schoolService.getAllStudents(
          pageInt,
          takeInt,
          schoolIdInt,
          classIdInt,
          filterTeachers,
        );
        return res.status(HttpStatus.OK).json(result);
      } else {
        // Non-admin users should only see students linked to their teacher_uuid
        if (!teacher_uuid) {
          return res
            .status(HttpStatus.BAD_REQUEST)
            .json({ message: 'Teacher UUID is required for non-admin users' });
        }

        // Fetch teacher_id using teacher_uuid
        const teacher = await this.schoolService.getTeacherByUUID(teacher_uuid);
        if (!teacher) {
          return res
            .status(HttpStatus.NOT_FOUND)
            .json({ message: 'Teacher not found' });
        }

        const teacher_id = teacher.id;

        // Fetch student_ids associated with the teacher_id
        const studentIds =
          await this.schoolService.getStudentIdsByTeacherId(teacher_id);

        if (studentIds.length === 0) {
          return res
            .status(HttpStatus.OK)
            .json({ students: [], totalCount: 0 });
        }

        // Fetch students using the student_ids
        const result = await this.schoolService.getStudentsByIds(
          studentIds,
          pageInt,
          takeInt,
          schoolIdInt,
          classIdInt,
          filterTeachers,
        );

        return res.status(HttpStatus.OK).json(result);
      }
    } catch (error) {
      console.error('Error fetching students:', error);
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: 'Internal server error' });
    }
  }
  @Get('/teacher-ids')
  @ApiOperation({
    summary: 'Get teacher IDs by UUIDs',
    description: 'Fetches teacher IDs from the database using their UUIDs.',
  })
  @ApiResponse({
    status: 200,
    description: 'List of teacher IDs with their corresponding UUIDs.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request.',
  })
  async getTeacherIdsByUUIDs(
    @Query('uuids') uuids: string,
    @Res() res: Response,
  ) {
    if (!uuids) {
      return res
        .status(HttpStatus.BAD_REQUEST)
        .json({ message: 'UUIDs query parameter is required' });
    }

    const uuidArray = uuids.split(',');

    try {
      const teacherIds =
        await this.schoolService.getTeacherIdsByUUIDs(uuidArray);
      return res.status(HttpStatus.OK).json(teacherIds);
    } catch (error) {
      console.error('Error fetching teacher IDs:', error);
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: 'Internal server error' });
    }
  }

  @Get('/teacher/email-report/:teacherUUID')
  @ApiOperation({
    summary: 'Email report for a teacher',
    description: 'Email report for a teacher',
  })
  @ApiResponse({
    status: 200,
    description: 'sending teacher report to imvi support',
  })
  async emailReportForTeacher(
    @Param('teacherUUID') teacherUUID: string,
    @Res() res: Response,
  ) {
    await this.schoolService.emailReportForTeacher(teacherUUID);
    return res.status(HttpStatus.OK).json({ message: 'Email sent' });
  }

  @Get('/teacher/reports-frequency/:teacherUUID')
  @ApiOperation({
    summary: 'Get report frequency for a teacher',
    description:
      'Fetches the report frequency for a teacher from the database.',
  })
  @ApiResponse({
    status: 200,
    description: 'Frequency of reports for the teacher',
  })
  async getReportsFrequencyForTeacher(
    @Param('teacherUUID') teacherUUID: string,
    @Res() res: Response,
  ) {
    const frequency =
      await this.schoolService.getTeacherReportFrequency(teacherUUID);
    return res.status(HttpStatus.OK).json({ frequency });
  }

  @Post('/teacher/reports-frequency/:teacherUUID')
  async updateTeacherReportFrequency(
    @Param('teacherUUID') teacherUUID: string,
    @Body() reportFrequencyDto: updateTeacherReportFrequencyDto,
  ) {
    try {
      await this.schoolService.updateTeacherReportFrequency(
        teacherUUID,
        reportFrequencyDto.frequency,
      );

      return { message: 'Report frequency updated successfully' };
    } catch (error) {
      return { message: error.message, statusCode: HttpStatus.BAD_REQUEST };
    }
  }

  @Post('link-to-teachers-firebase')
  async linkUserToTeachers(
    @Body() linkUserToTeachersDto: LinkUserToTeachersDto,
  ) {
    try {
      await this.schoolService.linkStudentToTeachers(
        linkUserToTeachersDto.firebaseUid,
      );

      return { message: 'User successfully linked to teachers' };
    } catch (error) {
      if (error.message.includes('User not found')) {
        throw new HttpException('User not found', HttpStatus.NOT_FOUND);
      } else if (error.message.includes('Teacher not found')) {
        throw new HttpException('Teacher not found', HttpStatus.NOT_FOUND);
      }

      return { message: error.message, statusCode: HttpStatus.BAD_REQUEST };
    }
  }
  @Delete('/student')
  @ApiOperation({
    summary: 'Delete a student',
    description:
      'Deletes a student from the database and external service if applicable.',
  })
  @ApiResponse({
    status: 200,
    description: 'The student has been successfully deleted.',
  })
  @ApiResponse({
    status: 404,
    description: 'Student not found.',
  })
  async deleteStudent(
    @Query('studentUuid') studentUuid: string,
    @Res() res: Response,
  ) {
    try {
      const deleted = await this.schoolService.deleteUserByUUID(studentUuid);

      return res
        .status(HttpStatus.OK)
        .json({ message: 'Student deleted successfully' });
    } catch (error) {
      console.error('Error deleting student:', error);
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: 'Internal server error' });
    }
  }
  @Delete('/student/by-id')
  @ApiOperation({
    summary: 'Delete a student by ID',
    description:
      'Deletes a student from the SQL database who has not activated their account and has no Firebase UUID.',
  })
  @ApiResponse({
    status: 200,
    description: 'The student has been successfully deleted.',
  })
  @ApiResponse({
    status: 404,
    description: 'Student not found.',
  })
  async deleteStudentById(
    @Query('studentId') studentId: string,
    @Res() res: Response,
  ) {
    try {
      const deleted = await this.schoolService.deleteStudentById(
        parseInt(studentId, 10),
      );

      return res
        .status(HttpStatus.OK)
        .json({ message: 'Student deleted successfully from the database.' });
    } catch (error) {
      console.error('Error deleting student:', error);
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: 'Internal server error' });
    }
  }
  @Post('/create-school')
  @ApiOperation({
    summary: 'Create a new school',
    description: 'Creates a new school.',
  })
  @ApiResponse({
    status: 201,
    description: 'The school has been successfully created.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request.',
  })
  async createSchool(
    @Body() createSchoolDto: CreateSchoolDto,
    @Res() res: Response,
  ) {
    try {
      const result = await this.schoolService.createSchool(createSchoolDto);
      return res.status(HttpStatus.CREATED).json(result);
    } catch (error) {
      console.error('Error creating school:', error);
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: 'Internal server error' });
    }
  }

  @Post('/create-class')
  @ApiOperation({
    summary: 'Create a new class for a school',
    description:
      'Creates a new class and associates it with an existing school.',
  })
  @ApiResponse({
    status: 201,
    description: 'The class has been successfully created.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request.',
  })
  async createClass(
    @Body() createClassDto: CreateClassDto,
    @Res() res: Response,
  ) {
    try {
      const result = await this.schoolService.createClass(createClassDto);
      return res.status(HttpStatus.CREATED).json(result);
    } catch (error) {
      console.error('Error creating class:', error);
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ message: 'Internal server error' });
    }
  }
}
