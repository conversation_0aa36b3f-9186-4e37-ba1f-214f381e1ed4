import { Modu<PERSON> } from '@nestjs/common';
import { SchoolController } from './school.controller';
import { SchoolService } from './school.service';
import { FirebaseModule } from 'src/firebase/firebase.module';
import { MailModule } from 'src/mail/mail.module';
import { VergenceTestsModule } from 'src/vergenceTest/vergence_tests.module';
import { VergenceTestsService } from 'src/vergenceTest/vergence_tests.service';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [FirebaseModule, MailModule, VergenceTestsModule, HttpModule],
  controllers: [SchoolController],
  providers: [SchoolService, VergenceTestsService],
})
export class SchoolModule {}
