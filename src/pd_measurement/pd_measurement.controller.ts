import { Controller, Post, Body } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { MeasurementService } from './pd_measurement.service';
@ApiTags('PDmeasurement')
@Controller('PDmeasurement')
export class PDMeasurementController {
  constructor(private readonly measurementService: MeasurementService) {}
  @Post('/addPDMeasurementData')
  @ApiOperation({
    summary: 'Add new PD measurement data',
    description:
      'Adds data from the PD app to the database for future use from the study in Karolinska.',
  })
  @ApiResponse({
    status: 200,
    description: 'PD data successfully added.',
  })
  @ApiResponse({ status: 404, description: 'Data not saved correctly' })
  async addPDMeasurementData(
    @Body() body: { optician_number: number; measured_number: number },
  ) {
    const { optician_number, measured_number } = body;
    try {
      await this.measurementService.addPDMeasurementData(
        optician_number,
        measured_number,
      );
      return { message: 'PD data successfully added.' };
    } catch (error) {
      return { error: error.message + ' Data not saved correctly' };
    }
  }
}
