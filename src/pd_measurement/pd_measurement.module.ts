import { <PERSON>du<PERSON> } from '@nestjs/common';
import { WinstonModule } from 'nest-winston';
import * as winston from 'winston'; // Make sure to import winston
import { transports, format } from 'winston';
import { PDMeasurementController } from './pd_measurement.controller';
import { DatabaseModule } from 'src/database/database.module';
import { MeasurementService } from './pd_measurement.service';

@Module({
  imports: [DatabaseModule],
  controllers: [PDMeasurementController],
  providers: [MeasurementService],
})
export class PDMeasurementModule {}
