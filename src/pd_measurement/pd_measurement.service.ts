import { Injectable, Logger } from '@nestjs/common';
import { DatabaseService } from 'src/database/database.service';

@Injectable()
export class MeasurementService {
  private readonly logger = new Logger(MeasurementService.name);

  constructor(private database: DatabaseService) {}

  async addPDMeasurementData(optician_number: number, measured_number: number) {
    const result = await this.database.pdMeasurement.create({
      data: {
        optician_number,
        measured_number,
      },
    });
  }
}
