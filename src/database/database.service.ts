import { Injectable, OnModuleInit } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class DatabaseService extends PrismaClient implements OnModuleInit {
  async onModuleInit() {
    await this.$connect();
  }

  async query(sql: string, params: any[]): Promise<any[]> {
    try {
      const result = await this.$queryRawUnsafe(sql, ...params);
      return result as any[];
    } catch (error) {
      console.error('Database query error:', error);
      throw new Error('Database query failed.');
    }
  }
}
