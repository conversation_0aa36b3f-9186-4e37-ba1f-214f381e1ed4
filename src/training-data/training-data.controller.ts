import {
  <PERSON>,
  Post,
  Body,
  Get,
  Param,
  Logger,
  Query,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import {
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiQuery,
} from '@nestjs/swagger';
import { TrainingDataService } from './training-data.service';
import { TrainingDataDto } from './dto/training-questions-data.dto';
import { CreateTestDataDto } from './dto/create-test-data.dto';
import { UpdateTestDataDto } from './dto/update-test-data.dto';

@ApiTags('TrainingData')
@Controller('TrainingData')
export class TrainingDataController {
  constructor(private trainingDataService: TrainingDataService) {}

  @Post('/addPostTrainingData')
  @ApiOperation({
    summary: 'Add new post training data',
    description:
      'Adds data from the training app to the database for future use.',
  })
  @ApiResponse({
    status: 200,
    description: 'Training data successfully added.',
  })
  @ApiResponse({ status: 404, description: 'Data not saved correctly' })
  async addPostTrainingData(
    @Body()
    trainingDataDto: TrainingDataDto,
  ) {
    try {
      await this.trainingDataService.addPostTrainingData(trainingDataDto);
      return { message: 'Training data successfully added.' };
    } catch (error) {
      Logger.error('Error saving training question data:', error);
      return { error: error.message + ' Data not saved correctly' };
    }
  }

  @Get('/:userUUID/get-user-trained-today')
  @ApiOperation({
    summary: 'Get the most recent question date',
    description: 'Retrieves the most recent question date for the user.',
  })
  @ApiParam({
    name: 'userUUID',
    description:
      'The UUID of the user whose notification settings are being updated',
  })
  @ApiResponse({
    status: 200,
    description: 'Most recent question date retrieved.',
  })
  @ApiResponse({ status: 404, description: 'Data not found' })
  async getUserTrainedToday(
    @Param('userUUID')
    userUUID: string,
  ) {
    try {
      const recentQuestionDate =
        await this.trainingDataService.getHasUserTrainedToday(userUUID);
      return recentQuestionDate;
    } catch (error) {
      Logger.error('Error getting Recent Question Date:', error);
      return { error: error.message + ' Data not found' };
    }
  }
  @Post('letters')
  async createLetters(
    @Body() body: { items: Array<{ value: string; language: string }> },
  ) {
    const result = await this.trainingDataService.createLetters(body.items);
    return {
      message: 'Letters inserted successfully',
      count: result.count, // createMany returns {count: number}
    };
  }

  @Post('words')
  async createWords(
    @Body() body: { items: Array<{ value: string; language: string }> },
  ) {
    const result = await this.trainingDataService.createWords(body.items);
    return {
      message: 'Words inserted successfully',
      count: result.count,
    };
  }

  @Post('sentences')
  async createSentences(
    @Body() body: { items: Array<{ value: string; language: string }> },
  ) {
    const result = await this.trainingDataService.createSentences(body.items);
    return {
      message: 'Sentences inserted successfully',
      count: result.count,
    };
  }
  @Post('chain')
  async createChainItems(
    @Query('type') type: 'sentences' | 'letters' | 'words',
    @Body() body: { items: any[] },
  ) {
    const results = await this.trainingDataService.createChainItems(
      type,
      body.items,
    );
    return {
      message: `Chain items inserted successfully for type ${type}`,
      insertedCount: results.length,
    };
  }
  @Post('migrate-data')
  async migrateUserTests(@Body() body: { tests: any[] }) {
    // body.tests is an array of objects with: email, testDate, testType, score, correctAnswers, wrongAnswers, etc.
    const results = await this.trainingDataService.migrateUserTests(body.tests);
    return {
      message: 'User tests migrated successfully',
      insertedCount: results.length,
    };
  }

  @Get('chain-data')
  async getChainItems(
    @Query('type') type: 'letters' | 'words' | 'sentences',
    @Query('language') language: string,
  ): Promise<any[]> {
    return this.trainingDataService.getChainsByType(type, language);
  }
  @Post('create-test-data')
  async createTestData(@Body() createTestDataDto: CreateTestDataDto) {
    const testData =
      await this.trainingDataService.createTestData(createTestDataDto);
    return {
      message: 'Test data created successfully',
      data: testData,
    };
  }
  @Post('update-test')
  async updateTestData(@Body() updateTestDataDto: UpdateTestDataDto) {
    const updatedData =
      await this.trainingDataService.updateTestData(updateTestDataDto);
    return {
      message: 'Test data updated successfully',
      data: updatedData,
    };
  }

  @Get('/:userId/test-scores')
  @ApiOperation({
    summary: 'Get user test scores',
    description:
      'Retrieves all test scores for a user, grouped by test type and sorted by date',
  })
  @ApiParam({
    name: 'userId',
    required: true,
    description: 'Numeric ID of the user',
  })
  @ApiQuery({
    name: 'currentDate',
    required: false,
    description: 'Filter scores by specific date (YYYY-MM-DD)',
  })
  @ApiQuery({
    name: 'testType',
    required: false,
    description:
      'Filter scores by test type (e.g., "words", "letters", "sentences")',
  })
  @ApiResponse({
    status: 200,
    description: 'Test scores retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getUserTestScores(
    @Param('userId') userId: string,
    @Query('currentDate') currentDate?: string,
    @Query('testType') testType?: string,
  ) {
    try {
      const numericUserId = parseInt(userId, 10);
      if (isNaN(numericUserId)) {
        throw new BadRequestException('Invalid user ID format');
      }

      const scores = await this.trainingDataService.getUserTestScores(
        numericUserId,
        currentDate,
        testType,
      );
      return {
        success: true,
        data: scores,
        message: 'Test scores retrieved successfully',
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new NotFoundException(error.message);
      }
      if (error instanceof BadRequestException) {
        throw new BadRequestException(error.message);
      }
      Logger.error(`Error fetching user test scores: ${error.message}`);
      throw new InternalServerErrorException('Failed to retrieve test scores');
    }
  }
}
