import { <PERSON>du<PERSON> } from '@nestjs/common';
import { WinstonModule } from 'nest-winston';
import * as winston from 'winston'; // Make sure to import winston
import { transports, format } from 'winston';
import { DatabaseModule } from 'src/database/database.module';
import { TrainingDataController } from './training-data.controller';
import { TrainingDataService } from './training-data.service';
import { UserModule } from 'src/user/user.module';

@Module({
  imports: [DatabaseModule, UserModule],
  controllers: [TrainingDataController],
  providers: [TrainingDataService],
})
export class TrainingDataModule {}
