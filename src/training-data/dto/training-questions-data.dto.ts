import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsInt } from 'class-validator';

export class TrainingDataDto {
  @ApiProperty({
    required: true,
    description: 'user UUID',
  })
  @IsNotEmpty()
  userUUID: string;

  @ApiProperty({
    required: true,
    description: 'question ID',
  })
  @IsNotEmpty()
  question_id: number;

  @ApiProperty({
    required: true,
    description: 'question value',
  })
  @IsNotEmpty()
  questionValue: string;

  @ApiProperty({
    required: true,
    description: 'answer ID',
  })
  @IsNotEmpty()
  answer_id: number[];

  @ApiProperty({
    required: true,
    description: 'answer value',
  })
  @IsNotEmpty()
  answerValue: string[];

  @ApiProperty({
    required: true,
    description: 'total questions',
  })
  @IsInt()
  @IsNotEmpty()
  totalQuestions: number;
  @ApiProperty({
    required: true,
    description: 'after training checker',
  })
  @IsNotEmpty()
  afterTrainingChecker: boolean;
}
