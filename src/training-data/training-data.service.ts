import { Body, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DatabaseService } from 'src/database/database.service';
import { TrainingDataDto } from './dto/training-questions-data.dto';
import { UserService } from 'src/user/user.service';
import { CreateTestDataDto } from './dto/create-test-data.dto';
import { UpdateTestDataDto } from './dto/update-test-data.dto';
interface SentenceChainItem {
  sentenceId: number;
  language: string;
  value: string;
  correctPosition1: number;
  correctPosition2: number;
  correctAnswerTimes?: (number | string)[];
  wrongAnswerTimes?: (number | string)[];
}

interface LettersChainItem {
  letterId: number;
  language: string;
  value: string;
  correctPosition1: number;
  correctPosition2: number;
  correctAnswerTimes?: (number | string)[];
  wrongAnswerTimes?: (number | string)[];
}
interface WordsChainItem {
  wordId: number;
  language: string;
  value: string;
  correctPosition1: number;
  correctPosition2: number;
  correctAnswerTimes?: (number | string)[];
  wrongAnswerTimes?: (number | string)[];
}

@Injectable()
export class TrainingDataService {
  private readonly logger = new Logger(TrainingDataService.name);

  constructor(
    private databaseService: DatabaseService,
    private userService: UserService,
  ) {}

  async addPostTrainingData(@Body() dto: TrainingDataDto) {
    const methodName = 'addPostTrainingData';
    this.logger.log(
      `TrainingDataAPI - ${methodName}: Attempting to add training question data for User UUID: ${dto.userUUID}`,
    );
    try {
      const user = await this.databaseService.user.findUnique({
        where: {
          uuid: dto.userUUID,
        },
        select: {
          id: true,
        },
      });
      if (!user) {
        Logger.error(
          `TrainingDataAPI - ${methodName}: User with UUID ${dto.userUUID} not found`,
        );
      } else {
        const nextQuestionID = (dto.question_id % dto.totalQuestions) + 1;

        const surveyData = dto.answer_id.map((answerIndex, range) => ({
          user_id: user.id,
          question_id: dto.question_id,
          question: dto.questionValue,
          answer_id: answerIndex,
          answer: dto.answerValue[range],
          created_at: new Date(),
        }));

        const result = await this.databaseService.postTrainingData.createMany({
          data: surveyData,
        });

        if (dto.afterTrainingChecker === true) {
          await this.databaseService.user_additional_info.update({
            where: {
              user_id: user.id,
            },
            data: {
              training_question_id: nextQuestionID,
            },
          });
        }
        if (
          (dto.question_id === 1 || dto.question_id === 10) &&
          dto.answer_id.includes(1)
        ) {
          this.logger.log('User has double vision, updating additional info');
          await this.userService.updateUserField(
            dto.userUUID,
            'has_double_vision',
            true,
          );
        }

        Logger.log(
          `TrainingDataAPI - ${methodName}: Post Training Data Added: ${result}`,
        );
      }
    } catch (error) {
      Logger.error(
        `TrainingDataAPI - ${methodName}: Could not add post training data info to database: ${error}`,
      );
    }
  }

  async getHasUserTrainedToday(userUUID: string) {
    const methodName = 'getRecentQuestionDate';
    this.logger.log(
      `TrainingDataAPI - ${methodName}: Attempting to get recent question date for User UUID: ${userUUID}`,
    );
    try {
      const user = await this.databaseService.user.findUnique({
        where: {
          uuid: userUUID,
        },
        select: {
          id: true,
        },
      });
      if (!user) {
        Logger.error(
          `TrainingDataAPI - ${methodName}: User with UUID ${userUUID} not found`,
        );
        return false;
      } else {
        const result = await this.databaseService.postTrainingData.findMany({
          where: {
            user_id: user.id,
          },
          orderBy: {
            created_at: 'desc',
          },
          select: {
            question_id: true,
            created_at: true,
          },
        });
        if (result === null) {
          Logger.log(
            `TrainingDataAPI - ${methodName}: No training question answered yet`,
          );
          return false;
        } else {
          const currentDate = new Date().toISOString().split('T')[0];
          const filteredResults = result.filter((entry) => {
            const formattedDate = entry.created_at.toISOString().split('T')[0];
            return formattedDate === currentDate;
          });

          if (filteredResults.length > 0) {
            Logger.log(
              `TrainingDataAPI - ${methodName}: Training questions answered today: ${JSON.stringify(
                filteredResults,
              )}`,
            );
            return filteredResults;
          } else {
            Logger.log(
              `TrainingDataAPI - ${methodName}: No training questions answered today.`,
            );
            return [];
          }
        }
      }
    } catch (error) {
      Logger.error(
        `TrainingDataAPI - ${methodName}: Could not get recent question date from database: ${error}`,
      );
    }
  }
  // Insert multiple letters
  async createLetters(items: Array<{ value: string; language: string }>) {
    // Map each item to the createMany input
    const data = items.map((item) => ({
      value: item.value,
      language: item.language,
    }));

    // Prisma's createMany
    return this.databaseService.readingLetter.createMany({
      data,
      skipDuplicates: true, // optional if you want to ignore duplicates
    });
  }

  // Insert multiple words
  async createWords(items: Array<{ value: string; language: string }>) {
    const data = items.map((item) => ({
      value: item.value,
      language: item.language,
    }));

    return this.databaseService.readingWord.createMany({
      data,
      skipDuplicates: true,
    });
  }

  // Insert multiple sentences
  async createSentences(items: Array<{ value: string; language: string }>) {
    const data = items.map((item) => ({
      value: item.value,
      language: item.language,
    }));

    return this.databaseService.readingSentence.createMany({
      data,
      skipDuplicates: true,
    });
  }

  // 2) Single entry point for all chain types
  async createChainItems(
    type: 'sentences' | 'letters' | 'words',
    items: any[],
  ) {
    if (type === 'sentences') {
      return this.createSentencesChain(items as SentenceChainItem[]);
    } else if (type === 'letters') {
      return this.createLettersChain(items as LettersChainItem[]);
    } else if (type === 'words') {
      return this.createWordsChain(items as WordsChainItem[]);
    } else {
      throw new Error(`Invalid chain type: ${type}`);
    }
  }

  // 3) Sentences chain logic
  private async createSentencesChain(items: SentenceChainItem[]) {
    const results = [];
    for (const item of items) {
      // Insert into reading_sentences_chain
      const chain = await this.databaseService.readingSentencesChain.create({
        data: {
          sentenceId: item.sentenceId,
          language: item.language,
          sentence: item.value,
          correctPosition1: item.correctPosition1,
          correctPosition2: item.correctPosition2,
        },
      });

      // Insert correct times
      if (item.correctAnswerTimes && item.correctAnswerTimes.length > 0) {
        await this.databaseService.readingSentencesChainCorrectTime.createMany({
          data: item.correctAnswerTimes.map((time) => ({
            chainId: chain.id,
            time: typeof time === 'string' ? parseFloat(time) : time,
          })),
        });
      }

      // Insert wrong times
      if (item.wrongAnswerTimes && item.wrongAnswerTimes.length > 0) {
        await this.databaseService.readingSentencesChainWrongTime.createMany({
          data: item.wrongAnswerTimes.map((time) => ({
            chainId: chain.id,
            time: typeof time === 'string' ? parseFloat(time) : time,
          })),
        });
      }

      results.push(chain);
    }
    return results;
  }

  // 4) Letters chain logic
  private async createLettersChain(items: LettersChainItem[]) {
    const results = [];
    for (const item of items) {
      // Insert into reading_letters_chain
      const chain = await this.databaseService.readingLettersChain.create({
        data: {
          letterId: item.letterId,
          language: item.language,
          letter: item.value,
          correctPosition1: item.correctPosition1,
          correctPosition2: item.correctPosition2,
        },
      });

      // Insert correct times
      if (item.correctAnswerTimes && item.correctAnswerTimes.length > 0) {
        await this.databaseService.readingLettersChainCorrectTime.createMany({
          data: item.correctAnswerTimes.map((time) => ({
            chainId: chain.id,
            time: typeof time === 'string' ? parseFloat(time) : time,
          })),
        });
      }

      // Insert wrong times
      if (item.wrongAnswerTimes && item.wrongAnswerTimes.length > 0) {
        await this.databaseService.readingLettersChainWrongTime.createMany({
          data: item.wrongAnswerTimes.map((time) => ({
            chainId: chain.id,
            time: typeof time === 'string' ? parseFloat(time) : time,
          })),
        });
      }

      results.push(chain);
    }
    return results;
  }

  // 5) Words chain logic
  private async createWordsChain(items: WordsChainItem[]) {
    const results = [];
    for (const item of items) {
      // Insert into reading_words_chain
      const chain = await this.databaseService.readingWordsChain.create({
        data: {
          wordId: item.wordId,
          language: item.language,
          word: item.value,
          correctPosition1: item.correctPosition1,
          correctPosition2: item.correctPosition2,
        },
      });

      // Insert correct times
      if (item.correctAnswerTimes && item.correctAnswerTimes.length > 0) {
        await this.databaseService.readingWordsChainCorrectTime.createMany({
          data: item.correctAnswerTimes.map((time) => ({
            chainId: chain.id,
            time: typeof time === 'string' ? parseFloat(time) : time,
          })),
        });
      }

      // Insert wrong times
      if (item.wrongAnswerTimes && item.wrongAnswerTimes.length > 0) {
        await this.databaseService.readingWordsChainWrongTime.createMany({
          data: item.wrongAnswerTimes.map((time) => ({
            chainId: chain.id,
            time: typeof time === 'string' ? parseFloat(time) : time,
          })),
        });
      }

      results.push(chain);
    }
    return results;
  }
  // Main method that handles the migration for an array of test docs
  async migrateUserTests(tests: any[]) {
    const results = [];

    for (const testDoc of tests) {
      // 1) Find user ID by email
      const userRecord =
        await this.databaseService.user_additional_info.findFirst({
          where: { email: testDoc.email },
          select: { user_id: true },
        });
      if (!userRecord) {
        console.warn(`No user found for email: ${testDoc.email}, skipping...`);
        continue;
      }
      const userId = userRecord.user_id;

      // 2) Convert and validate testDate
      const rawTestDate = testDoc.testDate;
      let testDate: Date;
      if (
        rawTestDate &&
        typeof rawTestDate === 'object' &&
        rawTestDate._seconds
      ) {
        // Firestore Timestamp represented as plain object
        testDate = new Date(rawTestDate._seconds * 1000);
      } else if (rawTestDate && typeof rawTestDate.toDate === 'function') {
        testDate = rawTestDate.toDate();
      } else {
        testDate = new Date(rawTestDate);
      }
      if (isNaN(testDate.getTime())) {
        console.warn(
          `Invalid test date "${JSON.stringify(rawTestDate)}" for email ${
            testDoc.email
          }, skipping...`,
        );
        continue;
      }

      // 3) Insert a row in reading_test_data
      const testData = await this.databaseService.readingTestData.create({
        data: {
          userId: userId,
          testDate: testDate, // already converted Date object
          language: testDoc.language || 'sv',
          testType: testDoc.testType, // e.g., "words"
          score:
            typeof testDoc.score === 'string'
              ? parseInt(testDoc.score, 10)
              : testDoc.score,
        },
      });

      // 4) Insert correct times
      if (testDoc.correctAnswers && testDoc.correctAnswers.length > 0) {
        for (const answer of testDoc.correctAnswers) {
          const chainId = await this.findChainId(answer, testDoc.testType);
          if (!chainId) {
            console.warn(
              `No chain_id found for answer: ${JSON.stringify(answer)}`,
            );
            continue;
          }
          const correctValue =
            testDoc.testType === 'sentences'
              ? answer.value !== undefined
                ? answer.value
                : 1
              : 1;
          await this.databaseService.userCorrectAnswerTime.create({
            data: {
              testDataId: testData.id,
              chainId: chainId,
              time:
                typeof answer.time === 'string'
                  ? parseFloat(answer.time)
                  : answer.time,
              value: correctValue,
            },
          });
        }
      }

      // 5) Insert wrong times
      if (testDoc.wrongAnswers && testDoc.wrongAnswers.length > 0) {
        for (const answer of testDoc.wrongAnswers) {
          const chainId = await this.findChainId(answer, testDoc.testType);
          if (!chainId) {
            console.warn(
              `No chain_id found for wrong answer: ${JSON.stringify(answer)}`,
            );
            continue;
          }
          await this.databaseService.userWrongAnswerTime.create({
            data: {
              testDataId: testData.id,
              chainId: chainId,
              time:
                typeof answer.time === 'string'
                  ? parseFloat(answer.time)
                  : answer.time,
              value: 0,
            },
          });
        }
      }

      results.push(testData);
    }

    return results;
  }

  // Helper to find chain_id in the appropriate chain table
  private async findChainId(
    answer: any,
    testType: string,
  ): Promise<number | null> {
    // answer has a 'time' plus either 'letter', 'word', or 'chain' depending on testType
    if (testType === 'letters') {
      // e.g., 'letter' field
      const letter = answer.letter;
      if (!letter) return null;
      const row = await this.databaseService.readingLettersChain.findFirst({
        where: { letter: letter },
        select: { id: true },
      });
      return row ? row.id : null;
    } else if (testType === 'words') {
      const word = answer.word;
      if (!word) return null;
      const row = await this.databaseService.readingWordsChain.findFirst({
        where: { word: word },
        select: { id: true },
      });
      return row ? row.id : null;
    } else if (testType === 'sentences') {
      const chain = answer.chain; // or 'sentence' if you prefer
      if (!chain) return null;
      const row = await this.databaseService.readingSentencesChain.findFirst({
        where: { sentence: chain },
        select: { id: true },
      });
      return row ? row.id : null;
    }
    return null;
  }

  async getChainsByType(
    type: 'letters' | 'words' | 'sentences',
    language: string,
  ): Promise<any[]> {
    if (type === 'letters') {
      const chains = await this.databaseService.readingLettersChain.findMany({
        where: { language: language },
      });
      return chains.map((c) => ({
        id: c.id,
        correctPosition: [c.correctPosition1, c.correctPosition2],
        lettersChain: c.letter,
        language: c.language,
      }));
    } else if (type === 'words') {
      const chains = await this.databaseService.readingWordsChain.findMany({
        where: { language: language },
      });
      return chains.map((c) => ({
        id: c.id,
        correctPosition: [c.correctPosition1, c.correctPosition2],
        wordsChain: c.word,
        language: c.language,
      }));
    } else if (type === 'sentences') {
      const chains = await this.databaseService.readingSentencesChain.findMany({
        where: { language: language },
      });
      return chains.map((c) => ({
        id: c.id,
        correctPosition: [c.correctPosition1, c.correctPosition2],
        sentencesChain: c.sentence,
        language: c.language,
      }));
    } else {
      throw new Error(`Invalid chain type: ${type}`);
    }
  }

  async createTestData(dto: CreateTestDataDto) {
    let userId: number;

    // If userId is not provided, try to find user by email
    if (!dto.userId) {
      if (!dto.email) {
        throw new NotFoundException('Either userId or email must be provided');
      }

      const userInfo =
        await this.databaseService.user_additional_info.findFirst({
          where: { email: dto.email },
          select: { user_id: true },
        });

      if (!userInfo) {
        throw new NotFoundException(`User not found with email: ${dto.email}`);
      }

      userId = userInfo.user_id;
    } else {
      userId = Number(dto.userId);
    }

    // Convert the testDate from string to Date object
    const testDate = new Date(dto.testDate);
    if (isNaN(testDate.getTime())) {
      throw new Error(`Invalid testDate: ${dto.testDate}`);
    }

    // Create the test data row with score set to 0
    const testData = await this.databaseService.readingTestData.create({
      data: {
        userId,
        testDate,
        language: dto.language,
        testType: dto.testType,
        score: 0,
      },
    });

    return testData;
  }

  async updateTestData(dto: UpdateTestDataDto) {
    // Update the reading_test_data row with the new score.
    const testData = await this.databaseService.readingTestData.update({
      where: { id: dto.test_id },
      data: { score: dto.score },
    });
    if (!testData) {
      throw new NotFoundException(`Test data with id ${dto.test_id} not found`);
    }

    // Insert correct answer times.
    if (dto.correctAnswers && dto.correctAnswers.length > 0) {
      await this.databaseService.userCorrectAnswerTime.createMany({
        data: dto.correctAnswers.map((ans) => {
          return {
            testDataId: dto.test_id,
            chainId: ans.chain_id,
            time: ans.time,
            value: ans.value,
          };
        }),
      });
    }

    // Insert wrong answer times.
    if (dto.wrongAnswers && dto.wrongAnswers.length > 0) {
      await this.databaseService.userWrongAnswerTime.createMany({
        data: dto.wrongAnswers.map((ans) => ({
          testDataId: dto.test_id,
          chainId: ans.chain_id,
          time: ans.time,
          value: ans.value,
        })),
      });
    }

    return testData;
  }

  async getUserTestScores(
    userId: number,
    currentDate?: string,
    testType?: string,
  ) {
    const methodName = 'getUserTestScores';
    try {
      // Validate user exists
      const user = await this.databaseService.user.findUnique({
        where: { id: userId },
        select: { id: true },
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Get all test scores for the user
      const testScores = await this.databaseService.readingTestData.findMany({
        where: {
          userId: userId,
          ...(currentDate && {
            testDate: {
              gte: new Date(currentDate),
              lt: new Date(
                new Date(currentDate).setDate(
                  new Date(currentDate).getDate() + 1,
                ),
              ),
            },
          }),
          ...(testType && {
            testType: testType,
          }),
        },
        select: {
          id: true,
          testType: true,
          score: true,
          testDate: true,
          language: true,
        },
        orderBy: {
          testDate: 'desc',
        },
      });

      // Group scores by test date
      const groupedScores = testScores.reduce(
        (acc, test) => {
          // Format date to YYYY-MM-DD
          const dateKey = test.testDate.toISOString().split('T')[0];

          if (!acc[dateKey]) {
            acc[dateKey] = [];
          }

          acc[dateKey].push({
            id: test.id,
            testType: test.testType,
            score: test.score,
            testDate: test.testDate,
            language: test.language,
          });

          return acc;
        },
        {} as Record<
          string,
          Array<{
            id: number;
            testType: string;
            score: number;
            testDate: Date;
            language: string;
          }>
        >,
      );

      // Convert to array format and sort by date (newest first)
      const sortedResults = Object.entries(groupedScores)
        .map(([date, tests]) => ({
          date,
          tests: tests.sort(
            (a, b) => b.testDate.getTime() - a.testDate.getTime(),
          ),
        }))
        .sort(
          (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
        );

      return sortedResults;
    } catch (error) {
      this.logger.error(
        `${methodName}: Error fetching test scores for user ${userId}: ${error.message}`,
      );
      throw error;
    }
  }
}
