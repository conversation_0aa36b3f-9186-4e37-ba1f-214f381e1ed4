import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, Injectable } from '@nestjs/common';
import { NotificationResponseDto } from '../dto/notifications.dto';

@Injectable()
@WebSocketGateway({
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },
  namespace: '/purchases/notifications',
})
export class NotificationGateway
  implements OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(NotificationGateway.name);
  private readonly connectedClients = new Map<string, Set<string>>();
  private readonly adminRoom = 'admin_notifications';

  handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);

    // Clean up any rooms this client was in
    this.connectedClients.forEach((clients, userId) => {
      if (clients.has(client.id)) {
        clients.delete(client.id);
        if (clients.size === 0) {
          this.connectedClients.delete(userId);
        }
      }
    });
  }

  @SubscribeMessage('joinNotificationRoom')
  handleJoinRoom(client: Socket, userId: string) {
    try {
      // Initialize user's connected clients set if it doesn't exist
      if (!this.connectedClients.has(userId)) {
        this.connectedClients.set(userId, new Set());
      }

      // Add this client to the user's set of connected clients
      this.connectedClients.get(userId).add(client.id);

      // Join the room
      client.join(`notification_${userId}`);
      this.logger.log(
        `Client ${client.id} joined notification room for user ${userId}`,
      );
    } catch (error) {
      this.logger.error(`Error joining room: ${error.message}`);
    }
  }

  @SubscribeMessage('joinAdminNotifications')
  handleJoinAdminRoom(client: Socket) {
    try {
      client.join(this.adminRoom);
      this.logger.log(`Client ${client.id} joined admin notifications room`);
    } catch (error) {
      this.logger.error(`Error joining admin room: ${error.message}`);
    }
  }

  @SubscribeMessage('leaveAdminNotifications')
  handleLeaveAdminRoom(client: Socket) {
    try {
      client.leave(this.adminRoom);
      this.logger.log(`Client ${client.id} left admin notifications room`);
    } catch (error) {
      this.logger.error(`Error leaving admin room: ${error.message}`);
    }
  }

  @SubscribeMessage('leaveNotificationRoom')
  handleLeaveRoom(client: Socket, userId: string) {
    try {
      // Remove from room
      client.leave(`notification_${userId}`);

      // Remove from connected clients
      if (this.connectedClients.has(userId)) {
        this.connectedClients.get(userId).delete(client.id);
        if (this.connectedClients.get(userId).size === 0) {
          this.connectedClients.delete(userId);
        }
      }

      this.logger.log(
        `Client ${client.id} left notification room for user ${userId}`,
      );
    } catch (error) {
      this.logger.error(`Error leaving room: ${error.message}`);
    }
  }

  // Method to emit new notifications to specific user
  async sendNotification(
    userId: string,
    notification: NotificationResponseDto,
  ): Promise<void> {
    try {
      this.server
        .to(`notification_${userId}`)
        .emit('newNotification', notification);
      this.logger.log(`Notification sent to user ${userId}`);
    } catch (error) {
      this.logger.error(`Error sending notification: ${error.message}`);
    }
  }

  // Method to emit new notifications to admin clients
  async broadcastAdminNotification(
    notification: NotificationResponseDto,
  ): Promise<void> {
    try {
      this.server.to(this.adminRoom).emit('newAdminNotification', notification);
      this.logger.log('Admin notification broadcasted');
    } catch (error) {
      this.logger.error(
        `Error broadcasting admin notification: ${error.message}`,
      );
    }
  }

  // Method to emit notification updates to admin clients
  async broadcastNotificationUpdate(
    notification: NotificationResponseDto,
  ): Promise<void> {
    try {
      this.server.to(this.adminRoom).emit('notificationUpdated', notification);
      this.logger.log(
        `Notification update broadcasted for ID: ${notification.id}`,
      );
    } catch (error) {
      this.logger.error(
        `Error broadcasting notification update: ${error.message}`,
      );
    }
  }

  // Utility method to check if a user has any connected clients
  isUserConnected(userId: string): boolean {
    return (
      this.connectedClients.has(userId) &&
      this.connectedClients.get(userId).size > 0
    );
  }
}
