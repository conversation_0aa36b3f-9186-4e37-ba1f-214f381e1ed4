import {
  <PERSON>,
  Get,
  Param,
  Patch,
  Delete,
  Query,
  HttpStatus,
  Logger,
  Body,
  HttpException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { NotificationService } from './notification.service';
import {
  NotificationResponseDto,
  NotificationQueryDto,
  MarkNotificationReadDto,
  NotificationCountDto,
} from '../dto/notifications.dto';

@ApiTags('Notifications')
@Controller('purchases/notifications')
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}
  private readonly logger = new Logger(NotificationController.name);

  @Get()
  @ApiOperation({ summary: 'Get all notifications with optional filtering' })
  @ApiResponse({ status: HttpStatus.OK, type: [NotificationResponseDto] })
  async getAll(
    @Query() query: NotificationQueryDto,
  ): Promise<NotificationResponseDto[]> {
    return this.notificationService.getAllNotifications(query);
  }

  @Get('unread')
  @ApiOperation({ summary: 'Get all unread notifications' })
  @ApiResponse({ status: HttpStatus.OK, type: [NotificationResponseDto] })
  async getAllUnread(): Promise<NotificationResponseDto[]> {
    return this.notificationService.getAllUnreadNotifications();
  }

  @Get('noti/:id')
  @ApiOperation({ summary: 'Get a specific notification' })
  @ApiResponse({ status: HttpStatus.OK, type: NotificationResponseDto })
  async findOne(@Param('id') id: string): Promise<NotificationResponseDto> {
    return this.notificationService.findOne(parseInt(id));
  }

  @Patch(':id/read')
  @ApiOperation({ summary: 'Mark a notification as read' })
  @ApiResponse({ status: HttpStatus.OK, type: NotificationResponseDto })
  async markAsRead(
    @Param('id') id: string,
    @Body() markReadDto: MarkNotificationReadDto,
  ): Promise<NotificationResponseDto> {
    return this.notificationService.markAsRead(parseInt(id), markReadDto);
  }

  @Patch('mark-all-read')
  @ApiOperation({ summary: 'Mark all notifications as read' })
  @ApiResponse({ status: HttpStatus.OK, type: [NotificationResponseDto] })
  async markAllAsRead(): Promise<NotificationResponseDto[]> {
    return this.notificationService.markAllAsRead();
  }

  @Get('count')
  @ApiOperation({ summary: 'Get notification counts' })
  @ApiResponse({
    status: HttpStatus.OK,
    type: NotificationCountDto,
    description: 'Returns total and unread notification counts',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Failed to fetch notification counts',
  })
  async getCount(): Promise<NotificationCountDto> {
    try {
      return await this.notificationService.getCount();
    } catch (error) {
      this.logger.error('Failed to get notification counts:', error);
      throw new HttpException(
        'Failed to fetch notification counts',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a notification' })
  @ApiResponse({ status: HttpStatus.NO_CONTENT })
  async delete(@Param('id') id: string): Promise<void> {
    await this.notificationService.delete(parseInt(id));
  }
}
