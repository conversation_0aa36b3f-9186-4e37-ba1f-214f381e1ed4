import { Modu<PERSON> } from '@nestjs/common';
import { TasksService } from './tasks.service';
import { DatabaseModule } from 'src/database/database.module';
import { PurchasesService } from '../purchases.service';
import { RedisModule } from '../../redis/redis.module';
import { TasksController } from './tasks.controller';
import { PurchaseNotificationModule } from '../notifications/notification.module';

@Module({
  controllers: [TasksController],
  providers: [TasksService, PurchasesService],
  imports: [DatabaseModule, RedisModule, PurchaseNotificationModule],
})
export class TasksModule {}
