import { <PERSON>, <PERSON> } from '@nestjs/common';
import { TasksService } from './tasks.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Tasks')
@Controller('tasks')
export class TasksController {
  constructor(private readonly tasksService: TasksService) {}

  @Post('migrate-addresses')
  @ApiOperation({
    summary: 'Migrate WooCommerce addresses to purchase_additional_info',
  })
  @ApiResponse({
    status: 200,
    description: 'Migration completed successfully',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number' },
        migrated: { type: 'number' },
        skipped: { type: 'number' },
        errors: { type: 'number' },
      },
    },
  })
  async migrateAddresses() {
    return this.tasksService.migrateWooCommerceAddresses();
  }
}
