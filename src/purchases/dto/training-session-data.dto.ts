import { ApiProperty } from '@nestjs/swagger';
import {
  IsDate,
  IsString,
  IsOptional,
  IsInt,
  Min,
  IsNumber,
} from 'class-validator';
import { Type } from 'class-transformer';

export class TrainingSessionDataDto {
  @ApiProperty({
    description: 'Unique identifier for the training session',
  })
  @IsInt()
  @Min(1)
  id: number;

  @ApiProperty({
    description: 'User ID associated with the training session',
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  user_id?: number;

  @ApiProperty({
    description: 'Sequential number of the training session',
  })
  @IsInt()
  @Min(1)
  session_number: number;

  @ApiProperty({
    description: 'Duration of the training session in seconds',
  })
  @IsInt()
  @Min(0)
  session_duration: number;

  @ApiProperty({
    description: 'Start time of the training session',
  })
  @Type(() => Date)
  @IsDate()
  start_time: Date;

  @ApiProperty({
    description: 'Type of training session',
    required: false,
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiProperty({
    description: 'Source of the streaming content',
    required: false,
  })
  @IsOptional()
  @IsString()
  streamingSource?: string;

  @ApiProperty({
    description: 'Speed of the training',
    required: false,
    type: 'number',
    format: 'float',
  })
  @IsOptional()
  @IsNumber()
  speed?: number;

  @ApiProperty({
    description: 'Length of the pendulum',
    required: false,
    type: 'number',
    format: 'float',
  })
  @IsOptional()
  @IsNumber()
  pendlumLength?: number;

  @ApiProperty({
    description: 'Offset value for the training',
    required: false,
    type: 'number',
    format: 'float',
  })
  @IsOptional()
  @IsNumber()
  offset?: number;

  @ApiProperty({
    description: 'Time of oscillation',
    required: false,
    type: 'number',
    format: 'float',
  })
  @IsOptional()
  @IsNumber()
  oscillationTime?: number;

  @ApiProperty({
    description: 'ID of the device used for training',
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  deviceId?: number;
}
