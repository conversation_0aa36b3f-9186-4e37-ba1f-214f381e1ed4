import { ApiProperty } from '@nestjs/swagger';
import { TrainingSessionDataDto } from './training-session-data.dto';

export class UserAdditionalInfoDto {
  @ApiProperty({ required: false })
  name?: string;

  @ApiProperty({ required: false })
  email?: string;

  @ApiProperty({ required: false })
  age?: number;

  @ApiProperty({ required: false })
  vision_problem?: string;

  @ApiProperty({ required: false })
  optional_text?: string;

  @ApiProperty({ required: false })
  accept_newsletter?: boolean;

  @ApiProperty({ required: false })
  birthdate?: Date;

  @ApiProperty({ required: false })
  first_name?: string;

  @ApiProperty({ required: false })
  last_name?: string;

  @ApiProperty({ required: false })
  notification_hour?: number;
}

export class UserDto {
  @ApiProperty()
  id: number;

  @ApiProperty({ required: false })
  uuid?: string;

  @ApiProperty({ required: false })
  registered_on?: Date;

  @ApiProperty({ required: false })
  starred?: boolean;

  @ApiProperty({ required: false })
  type?: string;

  @ApiProperty({ required: false })
  deleted?: boolean;

  @ApiProperty({ required: false })
  additional_info?: UserAdditionalInfoDto;

  @ApiProperty({
    required: false,
    type: [TrainingSessionDataDto],
    description: 'Array of training session data associated with the user',
  })
  training_session_data?: TrainingSessionDataDto[];
}

export class PurchaseActivationDto {
  @ApiProperty()
  id: number;

  @ApiProperty()
  purchase_id: number;

  @ApiProperty()
  activation_date: Date;

  @ApiProperty()
  updated_at: Date;

  @ApiProperty({ required: false, nullable: true })
  user_id?: number;

  @ApiProperty({ required: false, type: () => UserDto })
  user?: UserDto;
}
