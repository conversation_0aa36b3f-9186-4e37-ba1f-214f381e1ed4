import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsInt } from 'class-validator';

export class LinkPurchaseActivationDto {
  @ApiProperty({
    description:
      'The unique identifier of the activation that needs to be linked.',
    required: true,
  })
  @IsNotEmpty()
  @IsInt()
  activationId: number;

  @ApiProperty({
    description: 'The UUID of the user to whom the activation will be linked.',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  uuid: string;
}
