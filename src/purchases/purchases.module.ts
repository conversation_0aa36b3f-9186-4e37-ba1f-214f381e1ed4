import { Module } from '@nestjs/common';
import { PurchasesService } from './purchases.service';
import { PurchasesController } from './purchases.controller';
import { DatabaseModule } from 'src/database/database.module';
import { AdminActivitiesModule } from '../admin_activities/adminActivities.module';
import { AdminAuthModule } from '../auth_admin/adminAuth.module';
import { RedisModule } from '../redis/redis.module';

@Module({
  controllers: [PurchasesController],
  providers: [PurchasesService],
  imports: [
    DatabaseModule,
    AdminActivitiesModule,
    AdminAuthModule,
    RedisModule,
  ],
})
export class PurchasesModule {}
