import { Purchase } from '@prisma/client';
import { ApiProperty } from '@nestjs/swagger';

export class PurchaseEntity implements Purchase {
  @ApiProperty()
  id: number;

  @ApiProperty()
  email: string;

  @ApiProperty()
  first_name: string;

  @ApiProperty()
  last_name: string;

  @ApiProperty()
  code: string;

  @ApiProperty()
  number_of_vr_glasses: number;

  @ApiProperty()
  number_of_licenses: number;

  @ApiProperty()
  duration: number;

  @ApiProperty()
  is_subscription: boolean;

  @ApiProperty()
  order_number: string;

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  updated_at: Date;
}
