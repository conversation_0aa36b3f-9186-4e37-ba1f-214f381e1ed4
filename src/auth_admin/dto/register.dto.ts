import {
  <PERSON><PERSON><PERSON>,
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Matches,
  Length,
  IsEnum,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { UserAdminRole } from '@prisma/client';

export class RegisterDto {
  @ApiProperty({
    description: 'The email address of the admin',
  })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  email: string;

  @ApiProperty({
    description: 'The hashed password for the admin account',
  })
  @IsString()
  @Length(60, 60, { message: 'Invalid bcrypt hash length' })
  @Matches(/^\$2[abxy]\$\d{1,2}\$[A-Za-z0-9./]{53}$/, {
    message: 'Invalid bcrypt hash format',
  })
  password: string;

  @ApiProperty({
    description: 'The full name of the admin',
  })
  @IsString()
  @MinLength(2, { message: 'Name must be at least 2 characters long' })
  @MaxLength(50, { message: 'Name must not exceed 50 characters' })
  name: string;

  @ApiProperty({
    enum: UserAdminRole,
    description: 'The role of the user (ADMIN or USER)',
  })
  @IsEnum(UserAdminRole, { message: 'Invalid role' })
  role: UserAdminRole;
}
