import { ApiProperty } from '@nestjs/swagger';
import { UserAdminRole } from '@prisma/client';

export class UserDto {
  @ApiProperty({ description: 'The user ID' })
  id: string;

  @ApiProperty({ description: 'The user name' })
  name: string;

  @ApiProperty({ description: 'The user email' })
  email: string;

  @ApiProperty({ description: 'The user role' })
  role: UserAdminRole;

  @ApiProperty({ description: 'The user email verifing email' })
  emailVerified: Date | null;
}

export class LoginResponseDto {
  @ApiProperty({ description: 'The user information' })
  user: UserDto;

  @ApiProperty({ description: 'The session token' })
  sessionToken: string;

  @ApiProperty({ description: 'The session expiration date' })
  expiresAt: Date;
}
