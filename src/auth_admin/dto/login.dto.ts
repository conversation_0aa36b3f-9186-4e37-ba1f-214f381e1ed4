import { ApiProperty } from '@nestjs/swagger';
import { IsE<PERSON>, IsNotEmpty, IsString } from 'class-validator';

export class LogInDto {
  @ApiProperty({ description: 'The email address of the user' })
  @IsNotEmpty()
  @IsEmail()
  readonly email: string;

  @ApiProperty({ description: 'The password of the user' })
  @IsNotEmpty()
  @IsString()
  readonly password: string;

  @ApiProperty({ description: 'The account type' })
  readonly accountType: string;
}
