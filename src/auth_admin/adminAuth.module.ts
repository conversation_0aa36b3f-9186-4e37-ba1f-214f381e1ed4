import { Modu<PERSON> } from '@nestjs/common';
import { AdminAuthService } from './adminAuth.service';
import { AdminAuthController } from './adminAuth.controller';
import { DatabaseModule } from '../database/database.module';
import { JwtModule } from '@nestjs/jwt';
import { MailModule } from '../mail/mail.module';
import { ConfigModule } from '@nestjs/config';
import { AuthGuard } from './adminAuth.guard';
import { AdminActivitiesModule } from '../admin_activities/adminActivities.module';

@Module({
  imports: [
    DatabaseModule,
    JwtModule.register({
      secret: process.env.AUTH_ADMIN_JWT_SECRET,
      signOptions: { expiresIn: '1d' },
    }),
    MailModule,
    ConfigModule,
    AdminActivitiesModule,
  ],
  controllers: [AdminAuthController],
  providers: [AdminAuthService, AuthGuard],
  exports: [AdminAuthService, JwtModule, AuthGuard],
})
export class AdminAuthModule {}
