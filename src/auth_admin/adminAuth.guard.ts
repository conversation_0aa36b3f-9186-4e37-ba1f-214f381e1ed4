import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { DatabaseService } from 'src/database/database.service';
import * as moment from 'moment';
import { verifySessionToken } from './utils';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(private database: DatabaseService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);
    if (!token) {
      throw new UnauthorizedException('No token provided');
    }

    try {
      const sessions = await this.database.login_session.findMany({
        where: {
          expires: {
            gt: moment().toISOString(),
          },
        },
        include: {
          user: true,
        },
      });

      const session = sessions.find((s) => {
        return verifySessionToken(token, s.sessionToken);
      });

      if (!session) {
        throw new UnauthorizedException('Invalid or expired session');
      }

      // Attach user to request
      request.user = {
        id: session.user.id,
        email: session.user.email,
        role: session.user.role,
        sessionId: session.id,
      };

      return true;
    } catch (error) {
      console.error('Session verification failed:', error);
      throw new UnauthorizedException('Invalid session');
    }
  }

  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
