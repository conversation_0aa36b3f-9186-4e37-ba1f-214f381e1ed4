import * as dotenv from 'dotenv';
dotenv.config();
export const jwtConstants = {
  secret: process.env.AUTH_ADMIN_JWT_SECRET,
};

export const FRONTEND_ROUTES = {
  LOGIN: '/sigin',
  EMAIL_VERIFICATION_SUCCESS: '/emailVerification/success',
  EMAIL_VERIFICATION_ERROR: '/emailVerification/error',
  RESET_PASSWORD: '/reset-password',
};

export const EMAIL_TEMPLATES = {
  VERIFICATION: {
    getHTML: (name: string, verificationLink: string) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Email Verification</h2>
        <p>Hello ${name},</p>
        <p>Thank you for registering. Please verify your email address by clicking the button below:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${verificationLink}" 
             style="background-color: #4CAF50; 
                    color: white; 
                    padding: 12px 24px; 
                    text-decoration: none; 
                    border-radius: 4px; 
                    display: inline-block;">
            Verify Email Address
          </a>
        </div>
        <p>Or copy and paste this link in your browser:</p>
        <p style="word-break: break-all;">${verificationLink}</p>
        <p>This verification link will expire in 24 hours.</p>
        <p>After verification, you will be redirected to the login page.</p>
        <p>If you didn't create an account, you can safely ignore this email.</p>
        <br>
        <p>Best regards,</p>
        <p>IMVI Team</p>
      </div>
    `,
    getText: (verificationLink: string) =>
      `Please verify your email address by clicking on the link below:\n\n${verificationLink}`,
    getSubject: () => 'Verify Your Email Address',
  },
  PASSWORD_RESET: {
    getHTML: (name: string, resetLink: string) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Password Reset Request</h2>
        <p>Hello ${name},</p>
        <p>We received a request to reset your password. Click the button below to create a new password:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${resetLink}" 
             style="background-color: #4CAF50; 
                    color: white; 
                    padding: 12px 24px; 
                    text-decoration: none; 
                    border-radius: 4px; 
                    display: inline-block;">
            Reset Password
          </a>
        </div>
        <p>Or copy and paste this link in your browser:</p>
        <p style="word-break: break-all;">${resetLink}</p>
        <p>This password reset link will expire in 1 hour.</p>
        <p>If you didn't request a password reset, you can safely ignore this email.</p>
        <br>
        <p>Best regards,</p>
        <p>IMVI Team</p>
      </div>
    `,
    getText: (resetLink: string) =>
      `Please reset your password by clicking on the link below:\n\n${resetLink}`,
    getSubject: () => 'Reset Your Password',
  },
};
