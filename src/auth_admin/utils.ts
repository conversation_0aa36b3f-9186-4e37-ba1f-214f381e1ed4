import * as crypto from 'crypto';

export function generateSessionToken(): string {
  return crypto.randomBytes(32).toString('hex');
}

export function hashSessionToken(token: string): string {
  const salt = crypto.randomBytes(16).toString('hex');
  const hash = crypto.createHash('sha256');
  hash.update(salt + token);
  return `${salt}:${hash.digest('hex')}`;
}

export function verifySessionToken(
  token: string,
  hashedToken: string,
): boolean {
  const [salt, storedHash] = hashedToken.split(':');
  const hash = crypto.createHash('sha256');
  hash.update(salt + token);
  const computedHash = hash.digest('hex');
  return computedHash === storedHash;
}
