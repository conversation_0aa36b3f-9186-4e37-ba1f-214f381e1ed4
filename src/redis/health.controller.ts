import { Controller, Get } from '@nestjs/common';
import { RedisService } from './redis.service';

@Controller('health')
export class HealthController {
  constructor(private readonly redisService: RedisService) {}

  @Get('redis')
  async checkRedisHealth() {
    const isHealthy = await this.redisService.getHealth();
    const memoryInfo = await this.redisService.getMemoryInfo();

    return {
      status: isHealthy ? 'healthy' : 'unhealthy',
      memoryInfo,
    };
  }
}
