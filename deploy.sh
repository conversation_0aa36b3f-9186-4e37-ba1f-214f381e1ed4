#!/bin/bash

# Check if an environment argument is passed
if [ -z "$1" ]; then
  echo "Usage: $0 <env>"
  echo "Example: $0 dev"
  echo "Example: $0 prod"
  exit 1
fi

ENV=$1

# Set variables based on the environment
if [ "$ENV" = "dev" ]; then
  APP_NAME="cloud-run-app-dev"
  DEPLOY_NAME="imvi-api-dev"
elif [ "$ENV" = "prod" ]; then
  APP_NAME="cloud-run-app-prod"
  DEPLOY_NAME="imvi-api-prod"
else
  echo "Invalid environment: $ENV"
  echo "Valid environments are 'dev' or 'prod'"
  exit 1
fi

# Build the Docker image
docker buildx build --platform linux/amd64 -t $APP_NAME .

# Tag the Docker image
docker tag $APP_NAME gcr.io/imvi-read/$APP_NAME:latest

# Push the Docker image to Google Container Registry
docker push gcr.io/imvi-read/$APP_NAME:latest

# Deploy the image to Google Cloud Run
gcloud run deploy $DEPLOY_NAME \
  --image gcr.io/imvi-read/$APP_NAME:latest \
  --platform managed \
  --allow-unauthenticated \
  --region europe-west2
