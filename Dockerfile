# Use an official Node runtime as a parent image
FROM node:20

# Set the working directory
WORKDIR /usr/src/app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install all dependencies (including dev for prisma)
RUN npm install

# Copy Prisma schema
COPY prisma ./prisma/

# Generate Prisma Client
RUN npx prisma generate

# Bundle app source
COPY . .

# Build the application
RUN npm run build


# Your app binds to port 8080, so expose it
EXPOSE 8080

# Define the command to run the app
CMD ["npm", "run", "start:prod"]
