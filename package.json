{"name": "imvi-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:local": "NODE_ENV=local nest start", "start:prod": "NODE_ENV=prod nest start", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@google-cloud/pubsub": "^4.3.0", "@google-cloud/storage": "^7.11.1", "@nestjs/axios": "^3.0.3", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "*", "@nestjs/microservices": "^10.4.17", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.4.17", "@nestjs/schedule": "^4.1.1", "@nestjs/swagger": "^7.1.16", "@nestjs/websockets": "^10.4.17", "@opensearch-project/opensearch": "^3.5.1", "@prisma/client": "^5.6.0", "bcp-47": "^2.1.0", "bcrypt": "^5.1.1", "chartjs-node-canvas": "^4.1.6", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "country-language": "^0.1.7", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "dotenv": "^16.4.5", "express": "^4.18.2", "firebase-admin": "^12.5.0", "ioredis": "^5.6.0", "kafkajs": "^2.2.4", "langtag-utils": "^2.0.2", "locale-codes": "^1.3.1", "moment": "^2.30.1", "nest-winston": "^1.9.4", "nestjs-opensearch": "^1.4.1", "node-mailjet": "^6.0.5", "pdfkit": "^0.15.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "stream": "^0.0.3", "swagger-ui-express": "^5.0.0", "winston": "^3.11.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.2.10", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "prisma": "^5.6.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}