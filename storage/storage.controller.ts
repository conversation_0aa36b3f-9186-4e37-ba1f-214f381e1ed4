import { <PERSON>, Get, HttpStatus, Param, Query, Res } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { StorageService } from './storage.service';

@ApiTags('storage')
@Controller('storage')
export class StorageController {
  constructor(private storageService: StorageService) {}

  @Get('/metadata')
  async getMetadata(
    @Query('folder') folderName: string,
    @Query('type') type: 'images' | 'videos' = 'images',
    @Res() res: Response,
  ) {
    try {
      const metadata = await this.storageService.getFolderMetadata(
        folderName,
        type,
      );
      res.status(HttpStatus.OK).json(metadata);
    } catch (error) {
      res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ error: error.message });
    }
  }

  @Get('/archive/:folder')
  async getArchive(
    @Param('folder') folderName: string,
    @Query('type') type: 'images' | 'videos' = 'images',
    @Res() res: Response,
  ) {
    try {
      const archive = await this.storageService.getArchive(folderName, type);
      res.setHeader('Content-Type', 'application/zip');
      res.status(HttpStatus.OK).send(archive);
    } catch (error) {
      res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .json({ error: error.message });
    }
  }
}
