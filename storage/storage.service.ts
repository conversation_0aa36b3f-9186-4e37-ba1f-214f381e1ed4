import { Injectable, Logger } from '@nestjs/common';
import { Storage } from '@google-cloud/storage';
import * as crypto from 'crypto';
import { ConfigService } from '@nestjs/config';

@Injectable({})
export class StorageService {
  private readonly logger = new Logger(StorageService.name);
  private storage: Storage;
  private bucketName: string;

  constructor(private configService: ConfigService) {
    this.storage = new Storage();
    this.bucketName = this.configService.get<string>('BUCKET_NAME');
  }
  setBucketName(bucketName: string) {
    this.bucketName = bucketName;
  }

  async getFolderMetadata(
    folderName: string,
    type: 'images' | 'videos' = 'images',
  ): Promise<{ updated: string; fileUrl: string }> {
    try {
      const fileName = this.getFilePath(folderName, type);
      const [metadata] = await this.storage
        .bucket(this.bucketName)
        .file(fileName)
        .getMetadata();
      const fileUrl = `https://storage.googleapis.com/${this.bucketName}/${fileName}`;
      return { updated: metadata.updated, fileUrl };
    } catch (error) {
      throw new Error(
        `Failed to fetch metadata for ${folderName}: ${error.message}`,
      );
    }
  }

  async getArchive(
    folderName: string,
    type: 'images' | 'videos' = 'images',
  ): Promise<Buffer> {
    try {
      const fileName = this.getFilePath(folderName, type);
      const [file] = await this.storage
        .bucket(this.bucketName)
        .file(fileName)
        .download();
      return file;
    } catch (error) {
      throw new Error(
        `Failed to fetch archive for ${folderName}: ${error.message}`,
      );
    }
  }

  private getFilePath(folderName: string, type: 'images' | 'videos'): string {
    if (type === 'images') {
      return `Training/Images/${folderName}/${folderName}.zip`;
    } else {
      return `Training/Videos/${folderName}/${folderName}.zip`;
    }
  }
}
